package com.chipon.configtool.net

import io.netty.buffer.Unpooled
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

/**
 * <AUTHOR>
 */
class SerialClientTest {

    @Disabled
    @Test
    fun `test serial client`() {
        val channel = SerialClient("COM1").connect()
        channel.writeAndFlush(Unpooled.buffer().writeLong(0x12345678)).await()
        channel.closeFuture().await()
    }
}