package com.chipon.configtool.core.parser

import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ContainerTest {

    @Test
    fun `invalid container color is detected`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main" color="#12Z45G"/>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("invalid color format") } shouldBe true
    }

    @Test
    fun `valid container color passes validation`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main" color="#FF00AA"/>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val errors = root.validate()
        (errors.isEmpty()) shouldBe true
    }
} 