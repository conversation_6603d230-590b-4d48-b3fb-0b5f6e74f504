package com.chipon.configtool.app

import com.chipon.configtool.app.render.DataTypeValidator
import com.chipon.configtool.core.model.element.DataType
import io.kotest.assertions.throwables.shouldThrow
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

/**
 * 数据类型验证器测试
 */
class DataTypeValidatorTest {

    @Test
    fun `test uint8 validation`() {
        // 正常范围内
        assertDoesNotThrow { DataTypeValidator.validate(127, DataType.uint8) }
        assertDoesNotThrow { DataTypeValidator.validate(0, DataType.uint8) }
        assertDoesNotThrow { DataTypeValidator.validate(255, DataType.uint8) }

        // 超出范围，应该抛出异常
        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(-10, DataType.uint8)
        }
        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(300, DataType.uint8)
        }
    }

    @Test
    fun `test int8 validation`() {
        // 正常范围内
        assertDoesNotThrow { DataTypeValidator.validate(127, DataType.int8) }
        assertDoesNotThrow { DataTypeValidator.validate(-128, DataType.int8) }

        // 超出范围应抛异常
        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(200, DataType.int8)
        }
        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(-200, DataType.int8)
        }
    }

    @Test
    fun `test uint16 validation`() {
        assertDoesNotThrow { DataTypeValidator.validate(65535, DataType.uint16) }

        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(-100, DataType.uint16)
        }
        shouldThrow<IllegalArgumentException> {
            DataTypeValidator.validate(70000, DataType.uint16)
        }
    }

    @Test
    fun `test int32 validation`() {
        shouldThrow<ClassCastException> {
            DataTypeValidator.validate(Long.MAX_VALUE, DataType.int32)
        }
    }

    @Test
    fun `test isValidForType`() {
        assertTrue(DataTypeValidator.isValidForType(127, DataType.uint8))
        assertFalse(DataTypeValidator.isValidForType(300, DataType.uint8))
        assertFalse(DataTypeValidator.isValidForType(-10, DataType.uint8))
        
        assertTrue(DataTypeValidator.isValidForType(-100, DataType.int8))
        assertTrue(DataTypeValidator.isValidForType(100, DataType.int8))
        assertFalse(DataTypeValidator.isValidForType(200, DataType.int8))
    }

} 