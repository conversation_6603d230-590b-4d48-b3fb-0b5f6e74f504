package com.chipon.configtool.app.render

import com.chipon.configtool.app.controls.LedControl
import com.chipon.configtool.app.groupValue
import com.chipon.configtool.app.render.UIRenderer.FAKE_RADIO_KEY
import com.chipon.configtool.app.render.UIRenderer.RenderEnv
import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.ComboItem
import com.chipon.configtool.core.model.control.Led
import com.chipon.configtool.core.model.element.MathDefinition
import javafx.beans.property.DoubleProperty
import javafx.beans.property.IntegerProperty
import javafx.event.EventTarget
import javafx.scene.Node
import javafx.scene.control.*
import javafx.scene.layout.Region
import javafx.scene.layout.VBox
import tornadofx.*

/**
 * <AUTHOR> Wu
 */

/* ---------------- Helper Methods ---------------- */

fun setupCommon(node: Node, element: BaseElement) {
    if (element is IColorable) {
        setupColor(node, element.color)
    }

    if (element is BaseUIControl && node is Region) {
        setupSize(node, element.size)
        if (element.readonly == 1) {
            node.isDisable = true
        }
    }
}

fun setupSize(node: Region, size: Size) {
    if (size.width > 0) {
        node.minWidth = size.width.toDouble()
    }
    if (size.height > 0) {
        // 对于按钮等控件，限制最大高度以减少垂直空间占用
        val maxHeight = when (node) {
            //暂时不控制按钮最大高度
            is Button -> size.height.toDouble()
            is Label -> minOf(size.height.toDouble(), 22.0)
            is CheckBox -> minOf(size.height.toDouble(), 25.0)
            is ComboBox<*> -> minOf(size.height.toDouble(), 28.0)
            is Spinner<*> -> minOf(size.height.toDouble(), 28.0)
            is TextField -> minOf(size.height.toDouble(), 28.0)
            else -> -1.0
        }
        //防止button太矮字体显示不全
        if (maxHeight != -1.0) {
            node.minHeight = maxHeight
            node.prefHeight = maxHeight
        }
    }
}

fun setupColor(node: Node, color: String?) {
    color?.let {
        if (node is Label) {
            node.style {
                textFill = c(it)
            }
        } else if (node is ProgressBar) {
            node.style {
                accentColor =  c(it)
            }
            node.style += "-color-progress-bar-fill: $it"
        } else {
            node.style {
                backgroundColor += c(it)
            }
        }

    }
}

fun EventTarget.setContext(element: BaseElement, env: RenderEnv) {
    val context: BaseContext = when (this) {
        is Node -> when (element) {
            //math没有子项, 这里特殊处理visible math label
            is MathDefinition -> MathContext(element)
            is BaseContainer -> ContainerContext(element)
            is Led -> LedContext(this as LedControl, element)
            is BaseUIControl -> UIContext(element, this)
            else -> throw NotImplementedError()
        }

        is Tab -> ContainerContext(element as BaseContainer)
        else -> throw IllegalStateException()
    }

    when (this) {
        is Node -> this.userData = context
        is Tab -> this.userData = context
    }

    registerToEnv(context, env)

    //bind Context.value to control state
    bindVarToControlState(this, context)
}

private fun bindVarToControlState(node: EventTarget, ctx: BaseContext) {
    when (node) {
        // CheckBox / Switch / Toggle (boolean <-> int)
        is Toggle, is CheckBox -> {
            val intProp = ctx.valueProperty as IntegerProperty
            val selectProperty = if (node is CheckBox) node.selectedProperty() else (node as Toggle).selectedProperty()
            selectProperty.addListener { _, _, sel ->
                val v = if (sel) 1 else 0
                if (intProp.get() != v) intProp.set(v)
            }
            // context → UI
            intProp.addListener { _, _, v ->
                val bool = v.toInt() != 0
                if (selectProperty.value != bool) selectProperty.value = bool
            }
            // initial sync: UI ➜ context
            intProp.set(if (selectProperty.value) 1 else 0)
        }
        // ComboBox<ComboItem> (object ↔ int)
        is ComboBox<*> -> {
            @Suppress("UNCHECKED_CAST")
            val combo = node as ComboBox<ComboItem>
            val intProp = ctx.valueProperty as IntegerProperty

            // UI → context
            combo.selectionModel.selectedItemProperty().addListener { _, _, item ->
                if (intProp.get() != item.value) intProp.set(item.value)
            }

            // context → UI
            intProp.addListener { _, _, v ->
                val target = combo.items.find { it.value == v.toInt() }
                if (combo.selectionModel.selectedItem != target) {
                    combo.selectionModel.select(target)
                }
            }

            // initial sync: UI ➜ context
            intProp.set(combo.selectionModel.selectedItem.value)
        }

        // Integer Spinner
        is Spinner<*> -> {
            if (node.valueFactory is SpinnerValueFactory.IntegerSpinnerValueFactory) {
                val factory = node.valueFactory as SpinnerValueFactory.IntegerSpinnerValueFactory
                val intProp = ctx.valueProperty as IntegerProperty
                // UI -> context
                factory.valueProperty().addListener { _, _, v ->
                    if (intProp.get() != v) intProp.set(v)
                }
                // context -> UI
                intProp.addListener { _, _, v ->
                    if (factory.valueProperty().get() != v) {
                        factory.valueProperty().set(v.toInt())
                    }
                }
                // initial sync: UI ➜ context
                intProp.set(factory.valueProperty().get())
            } else if (node.valueFactory is SpinnerValueFactory.DoubleSpinnerValueFactory) {
                val factory = node.valueFactory as SpinnerValueFactory.DoubleSpinnerValueFactory
                val doubleProp = ctx.valueProperty as DoubleProperty
                factory.valueProperty().addListener { _, _, v ->
                    if (doubleProp.get() != v) doubleProp.set(v)
                }
                doubleProp.addListener { _, _, v ->
                    if (factory.valueProperty().get() != v) {
                        factory.valueProperty().set(v.toDouble())
                    }
                }
                // initial sync
                doubleProp.set(factory.valueProperty().get())
            }
        }
    }

    // Radio group (VBox + ToggleGroup) value ↔ int
    if (node is VBox && node.isRadioGroup()) {
        val toggleGroup = node.toRadioGroup()
        val intProp = ctx.valueProperty as IntegerProperty

        // UI → context
        toggleGroup.selectedToggleProperty().addListener { _, _, toggle ->
            val v = toggle.groupValue() as Int
            if (intProp.get() != v) intProp.set(v)
        }

        // context → UI
        intProp.addListener { _, _, v ->
            val target = toggleGroup.toggles.find { (it.groupValue() as Int) == v.toInt() }
            if (target != null && toggleGroup.selectedToggle != target) {
                toggleGroup.selectToggle(target)
            }
        }

        // initial sync: UI ➜ context
        intProp.set(toggleGroup.selectedToggle.groupValue() as Int)
    }
    // Bind label text for visible math definitions
    if (node is Label && ctx is MathContext) {
        val format = ctx.math.format
        if (format != null) {
            node.textProperty().bind(ctx.valueProperty.asString(format) + ctx.math.unit)
        } else {
            node.textProperty().bind(ctx.valueProperty.asString() + ctx.math.unit)
        }
    }
}

fun registerToEnv(context: BaseContext, env: RenderEnv) {
    if (context.id != null) {
        if (env.defineContexts.put(context.id, context) != null) {
            throw IllegalArgumentException("ID声明重复：${context.id}")
        }
    }
    //可能包含依赖需要计算
    if (context is DerivedContext) {
        env.dependentContexts.add(context)
    }
}

fun EventTarget.getContext(): BaseContext? {
    return when (this) {
        is Node -> userData as? BaseContext
        is Tab -> userData as? ContainerContext
        else -> null
    }
}

fun VBox.isRadioGroup(): Boolean {
    return properties[FAKE_RADIO_KEY] is ToggleGroup
}

fun VBox.toRadioGroup(): ToggleGroup {
    return properties[FAKE_RADIO_KEY] as ToggleGroup
}
