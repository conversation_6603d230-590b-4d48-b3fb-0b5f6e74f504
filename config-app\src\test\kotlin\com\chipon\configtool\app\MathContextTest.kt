package com.chipon.configtool.app

import com.chipon.configtool.app.render.MathContext
import com.chipon.configtool.app.render.UIRenderer
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.beans.property.IntegerProperty
import javafx.stage.Stage
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit

class MathContextTest : TestCase() {
    private val primaryStage: Stage = FxToolkit.registerPrimaryStage()

    @Test
    fun `math context should evaluate and update when dependencies change`() {
        // Define a minimal profile with two variables and one math expression
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <var id="A"/>
                    <var id="B"/>
                    <math id="SUM" formula="A + B"/>
                </vbox>
            </uicfg>
        """.trimIndent()

        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixture { root ->
            val env = UIRenderer.buildView(profile, root)

            val ctxA = env.defineContexts["A"]!!
            val ctxB = env.defineContexts["B"]!!
            val sumCtx = env.dependentContexts.filterIsInstance<MathContext>().first { it.id == "SUM" }

            // Initial values should be zero
            sumCtx.value.toInt() shouldBe 0

            // Update variable A
            (ctxA.valueProperty as IntegerProperty).set(7)
            sumCtx.value.toInt() shouldBe 7

            // Update variable B
            (ctxB.valueProperty as IntegerProperty).set(5)
            sumCtx.value.toInt() shouldBe 12
        }
    }

    @Test
    fun `chained math contexts should update`() {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <var id="A"/>
                    <math id="B" formula="A * 2"/>
                    <math id="C" formula="B + 3"/>
                </vbox>
            </uicfg>
        """.trimIndent()

        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixture { root ->
            val env = UIRenderer.buildView(profile, root)

            val ctxA = env.defineContexts["A"]!!
            val ctxB = env.dependentContexts.filterIsInstance<MathContext>().first { it.id == "B" }
            val ctxC = env.dependentContexts.filterIsInstance<MathContext>().first { it.id == "C" }

            // Initial values: A = 0, B = 0, C = 3
            ctxB.value.toInt() shouldBe 0
            ctxC.value.toInt() shouldBe 3

            // Update A -> expect B and C to cascade
            (ctxA.valueProperty as IntegerProperty).set(4)
            ctxB.value.toInt() shouldBe 8
            ctxC.value.toInt() shouldBe 11
        }
    }
} 