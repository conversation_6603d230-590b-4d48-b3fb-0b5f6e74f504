package com.chipon.configtool.app.controls;

import javafx.geometry.Pos;
import javafx.scene.control.skin.ProgressBarSkin;
import javafx.scene.layout.StackPane;

/**
 * The skin for the {@link VerticalProgressBar}. This class handles the vertical
 * layout and rendering of the progress bar's components.
 */
public class VerticalProgressBarSkin extends ProgressBarSkin {

    /**
     * Creates a new skin instance for the given progress bar.
     * @param progressBar The control to skin.
     */
    public VerticalProgressBarSkin(VerticalProgressBar progressBar) {
        super(progressBar);

        // 设置进度条容器的对齐方式为底部对齐，实现从下到上的效果
        if (getChildren().size() > 1) {
            StackPane bar = (StackPane) getChildren().get(1);
            bar.setAlignment(Pos.BOTTOM_CENTER);
        }
    }

    /**
     * Lays out the child nodes (track and bar) vertically.
     */
    @Override
    protected void layoutChildren(double x, double y, double width, double height) {
        // The track (the first child) should fill the entire area.
        if (getChildren().isEmpty()) {
            return;
        }

        final StackPane track = (StackPane) getChildren().get(0);
        track.resizeRelocate(x, y, width, height);

        // The bar (the second child) is resized vertically based on progress.
        final StackPane bar = (StackPane) getChildren().get(1);
        double progress = getSkinnable().getProgress();

        if (progress < 0) { // Indeterminate progress
            bar.resizeRelocate(x, y, width, height);
        } else {
            final double barHeight = height * progress;
            // 将进度条定位在底部，实现从下到上的填充效果
            bar.resizeRelocate(x, y + height - barHeight, width, barHeight);
        }
    }

    /**
     * The preferred height of a vertical bar is equivalent to the preferred width
     * of a horizontal bar.
     */
    @Override
    protected double computePrefHeight(double w, double topInset, double rightInset, double bottomInset, double leftInset) {
        return super.computePrefWidth(w, topInset, rightInset, bottomInset, leftInset);
    }

    /**
     * The preferred width of a vertical bar is equivalent to the preferred height
     * of a horizontal bar.
     */
    @Override
    protected double computePrefWidth(double h, double topInset, double rightInset, double bottomInset, double leftInset) {
        return super.computePrefHeight(h, topInset, rightInset, bottomInset, leftInset);
    }
}