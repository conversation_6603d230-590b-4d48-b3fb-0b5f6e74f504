package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.parser.LockerDeserialize
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <combo>
 */
class UICombo : BaseUIControl(), IDefaultable<Int>, IValidatable {

    /**
     * The default selected item's value
     */
    override var default: Int = -1

    @JacksonXmlProperty(localName = "item")
    var items: List<ComboItem> = emptyList()

    fun findItem(value: Int): ComboItem? {
        return items.find { it.value == value }
    }

    override fun validate(): List<String> {
        //normalize default value
        if (items.all { it.value == 0 }) {
            items.forEachIndexed { index, item ->
                item.value = index
            }
        }

        //validate
        val errors = mutableListOf<String>()
        if (items.isEmpty()) {
            errors += "Combo has no <item> elements defined"
        }
        if (default != -1 && findItem(default) == null) {
            errors += "Bad default index: $default, expected [${items.map { it.value }.joinToString(" , ")}]"
        }
        return errors
    }
}

/**
 * <item>
 */
data class ComboItem(
    var label: String,

    /**
     * default value will base on index order
     */
    var value: Int,

    @set:JacksonXmlProperty(isAttribute = true)
    @set:JsonDeserialize(using = LockerDeserialize::class)
    override var lockon: LockOn? = null,
) : ILocker
