package com.chipon.configtool.core.parser

import com.chipon.configtool.core.logger
import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.*
import com.chipon.configtool.core.model.element.MathDefinition
import com.chipon.configtool.core.model.element.Timer
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonToken
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.dataformat.xml.deser.FromXmlParser
import com.google.common.collect.BiMap
import com.google.common.collect.HashBiMap

/**
 * Jackson module for handling polymorphic UI elements based on XML tag names.
 */
class ConfigElementModule : SimpleModule() {

    init {
        // Register deserializers for polymorphic elements
        addDeserializer(ProfileRoot::class.java, ProfileRootDeserializer())
        addDeserializer(GroupContainer::class.java, ContainerDeserializer(GroupContainer::class.java))
        addDeserializer(TabContainer::class.java, ContainerDeserializer(TabContainer::class.java))
        addDeserializer(GridContainer::class.java, ContainerDeserializer(GridContainer::class.java))
        addDeserializer(HorizontalContainer::class.java, ContainerDeserializer(HorizontalContainer::class.java))
        addDeserializer(VerticalContainer::class.java, ContainerDeserializer(VerticalContainer::class.java))
    }
}

val TAG_MAP: BiMap<String, Class<out BaseElement>> = HashBiMap.create(
    mapOf(
        // UI Controls
        "button" to UIButton::class.java,
        "checkbox" to UICheckbox::class.java,
        "switch" to UISwitch::class.java,
        "togglebutton" to UIToggle::class.java,
        "combo" to UICombo::class.java,
        "spinner" to UISpinner::class.java,
        "doublespinner" to UIDoubleSpinner::class.java,
        "text" to UIText::class.java,
        "textinput" to UITextInput::class.java,
        "led" to Led::class.java,
        "radio" to UIRadio::class.java,
        "vline" to VLine::class.java,
        "hline" to HLine::class.java,
        "progressbar" to UIProgressBar::class.java,

        // Containers
        "group" to GroupContainer::class.java,
        "tab" to TabContainer::class.java,
        "grid" to GridContainer::class.java,
        "hbox" to HorizontalContainer::class.java,
        "vbox" to VerticalContainer::class.java,

        // Elements
        "timer" to Timer::class.java,
        "math" to MathDefinition::class.java,
        "var" to Variable::class.java
    )
)

/**
 * Custom deserializer for ProfileRoot that handles polymorphic child elements
 */
class ProfileRootDeserializer : JsonDeserializer<ProfileRoot>() {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): ProfileRoot {
        val tagName = (p as FromXmlParser).staxReader.name.localPart
        if (tagName != "uicfg") {
            throw UnknownPropertyException(tagName)
        }
        val mapper = p.codec as ObjectMapper

        val profileRoot = ProfileRoot()

        var token: JsonToken? = p.nextToken()
        while (token != JsonToken.END_OBJECT) {
            val fieldName = p.currentName()
            // iterate to value
            token = p.nextToken()

            when (fieldName) {
                ProfileRoot::version.name -> profileRoot.version = p.text
                ProfileRoot::svd.name -> profileRoot.svd = mapper.readValue(p, SvdFile::class.java)
                else -> {
                    val elementClass: Class<out BaseElement>? = TAG_MAP[fieldName]
                    if (elementClass != null) {
                        if (BaseContainer::class.java.isAssignableFrom(elementClass)) {
                            val element = mapper.readValue(p, elementClass) as BaseContainer
                            profileRoot.children.add(element)
                        } else {
                            throw IllegalStateException("Only container elements are allowed in root: $fieldName")
                        }
                    } else {
                        logger.error("unknown uicfg field token: $fieldName")
                        // 未知字段，跳过
                        p.skipChildren()
                    }
                }
            }
            token = p.nextToken()
        }

        return profileRoot
    }
}

/**
 * Generic container deserializer that handles polymorphic child elements for all container types
 */
class ContainerDeserializer<T : BaseContainer>(private val clazz: Class<T>) : JsonDeserializer<T>() {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): T {
        val mapper = p.codec as ObjectMapper

        val container = clazz.getDeclaredConstructor().newInstance()

        var token: JsonToken? = p.nextToken()
        while (token != JsonToken.END_OBJECT) {
            val fieldName = p.currentName()
            // iterate to value
            token = p.nextToken()

            when (fieldName) {
                BaseElement::id.name -> container.id = p.text
                "label" -> if (container is IDisplayable) container.label = p.text
                BaseContainer::color.name -> container.color = p.text
                GridContainer::columns.name -> if (container is GridContainer) container.columns = p.valueAsInt
                else -> {
                    val elementClass = TAG_MAP[fieldName]
                    if (elementClass != null) {
                        if (token == JsonToken.START_ARRAY) {
                            // multiple children of same type
                            while (p.nextToken() != JsonToken.END_ARRAY) {
                                val child = mapper.readValue(p, elementClass)
                                container.children.add(child)
                            }
                        } else if (token == JsonToken.START_OBJECT || token == JsonToken.VALUE_STRING) {
                            val child = mapper.readValue(p, elementClass)
                            container.children.add(child)
                        } else {
                            throw IllegalStateException("Unexpectable token : $fieldName")
                        }
                    } else {
                        logger.error("Skipping unknown token: $fieldName")
                        p.skipChildren()
                    }
                }
            }

            token = p.nextToken()
        }
        return container as T
    }
}

class LockerDeserialize : JsonDeserializer<LockOn>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): LockOn? {
        val lockonString = p.text
        if (lockonString.isNullOrBlank()) {
            return null
        }

        val lockers: List<Locker> = lockonString.split(";")
            .filter { it.isNotBlank() }
            .map { condition ->
                val parts = condition.split("=")
                if (parts.size == 2) {
                    val id = parts[0].trim()
                    val value = parts[1].trim()
                    Locker(id, value.toInt())
                } else {
                    throw IllegalArgumentException("Invalid lockon condition format: $condition")
                }
            }
        if (lockers.isEmpty()) {
            return null
        }
        return LockOn(lockers)
    }
}

class IntRangeDeserializer : JsonDeserializer<Pair<Int, Int>>() {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Pair<Int, Int>? {
        val raw = p.text ?: return null
        val text = raw.trim()
        if (text.isEmpty()) {
            return null
        }
        val parts = text.split(";")
        if (parts.size != 2) {
            throw IllegalArgumentException("Invalid range format: '$raw', expected 'min;max'")
        }
        val min = parts[0].trim().toIntOrNull() ?: throw IllegalArgumentException("Invalid minimum value in range: '${parts[0]}'")
        val max = parts[1].trim().toIntOrNull() ?: throw IllegalArgumentException("Invalid maximum value in range: '${parts[1]}'")
        return Pair(min, max)
    }
}

/**
 * Deserializes a range expressed as "min;max" into a Pair<Double, Double>.
 * Accepts both integer and floating-point numbers. Whitespace around numbers is ignored.
 */
class DoubleRangeDeserializer : JsonDeserializer<Pair<Double, Double>>() {

    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Pair<Double, Double>? {
        val raw = p.text ?: return null
        val text = raw.trim()
        if (text.isEmpty()) {
            return null
        }
        val parts = text.split(";")
        if (parts.size != 2) {
            throw IllegalArgumentException("Invalid range format: '$raw', expected 'min;max'")
        }
        val min = parts[0].trim().toDoubleOrNull() ?: throw IllegalArgumentException("Invalid minimum value in range: '${parts[0]}'")
        val max = parts[1].trim().toDoubleOrNull() ?: throw IllegalArgumentException("Invalid maximum value in range: '${parts[1]}'")
        return Pair(min, max)
    }
}
