package com.chipon.configtool.net

import com.chipon.configtool.net.handler.PacketCodec
import com.chipon.configtool.net.packet.*
import com.chipon.configtool.net.transport.JSerialCommChannel
import com.chipon.configtool.net.transport.JSerialCommChannelOption.BAUD_RATE
import com.chipon.configtool.net.transport.JSerialCommDeviceAddress
import io.netty.bootstrap.Bootstrap
import io.netty.channel.Channel
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInitializer
import io.netty.channel.SimpleChannelInboundHandler
import io.netty.handler.logging.LogLevel
import io.netty.handler.logging.LoggingHandler

/**
 * Fake serial device that opens COM2 via Netty Bootstrap (JSerialCommChannel)
 * and responds to Ping / Read / Write messages using an in-memory register map.
 */
object FakeSerialServer {

    @JvmStatic
    fun main(args: Array<String>) {
        start()
    }

    fun start(
        portName: String = "COM2",
        memory: () -> MutableMap<Int, Any> = { mutableMapOf() }
    ) {
        try {
            val bootstrap = Bootstrap()
                .group(IClient.OIO_EVENT_LOOP_GROUP)
                .channel(JSerialCommChannel::class.java)
                .option(BAUD_RATE, 115200)
                .handler(object : ChannelInitializer<Channel>() {
                    override fun initChannel(ch: Channel) {
                        ch.pipeline().addFirst(LoggingHandler(LogLevel.DEBUG))
                        ch.pipeline().addLast(PacketCodec())
                        ch.pipeline().addLast(ServerHandler(memory()))
                    }
                })

            val future = bootstrap.connect(JSerialCommDeviceAddress(portName)).sync()
            println("Fake Serial Server running on $portName. Press Ctrl+C to exit.")
            future.channel().closeFuture().sync()
        } finally {
            IClient.OIO_EVENT_LOOP_GROUP.shutdownGracefully()
        }
    }
}

class ServerHandler(val memory: MutableMap<Int, Any>) : SimpleChannelInboundHandler<Packet>() {
    override fun channelRead0(ctx: ChannelHandlerContext, packet: Packet) {
        val msg = packet.payload
        when (msg) {
            is Ping -> ctx.writeAndFlush(Packet(packet.seq, Pong(msg.data)))

            is WritePayload -> {
                msg.data.forEach { memory[it.address] = it.data }
                ctx.writeAndFlush(Packet(packet.seq, StatusPayload(0)))
            }

            is ReadPayload -> {
                val list = msg.address.map { a ->
                    val v = memory.getOrDefault(a, 0)
                    VarList(a, 1, v as Long)
                }
                ctx.writeAndFlush(Packet(packet.seq, WritePayload(list)))
            }

            else -> ctx.writeAndFlush(Packet(packet.seq, StatusPayload(0x01))) // unsupported
        }
    }
}