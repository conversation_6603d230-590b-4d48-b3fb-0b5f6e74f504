package com.chipon.configtool.core.model.element

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IAction
import com.chipon.configtool.core.model.IColorable
import com.chipon.configtool.core.model.IValidatable
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <math>
 */
class MathDefinition : BaseUIControl(), IAction, IColorable, IValidatable {

    var visible: Boolean = true

    var type: DataType = DataType.uint8

    /**
     * @see java.util.Formatter
     */
    var format: String? = null

    var unit: String = ""

    lateinit var formula: String

    override var color: String? = null

    @JacksonXmlProperty(localName = "action")
    override var actions: List<Action> = emptyList()

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (!this::formula.isInitialized || formula.isBlank()) {
            errors.add("formula is required")
        }
        return errors
    }

    override fun toString(): String {
        return "math(" +
                "visible=$visible, " +
                "format=$format, " +
                "unit=$unit, " +
                "formula=$formula, " +
                "actions=$actions" +
                ")"
    }

}

enum class DataType(val size: Int) {
    int8(1), uint8(1),
    int16(2), uint16(2),
    int32(4), uint32(4),
    int64(8), uint64(8), double(8)
}