package com.chipon.configtool.app;

import javafx.scene.Cursor;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.Region;

/**
 * {@link DragResizer} can be used to add mouse listeners to a {@link Region} and make it resizable by the user by clicking and dragging the border in the same way as a window.
 * <p>
 * Only height resizing is currently implemented. Usage: <pre>DragResizer.makeResizable(myAnchorPane);</pre>
 *
 * <AUTHOR>
 */
public class DragResizer {

    public enum Direction {
        TOP, RIGHT, BOTTOM, LEFT
    }

    /**
     * The margin around the control that a user can click in to start resizing the region.
     */
    private static final int RESIZE_MARGIN = 8;

    private final Direction direction;
    private final Region region;

    private double y;

    private boolean dragging;
    private Cursor preCursor;

    private DragResizer(Direction direction, Region aRegion) {
        this.direction = direction;
        region = aRegion;
    }

    public static void makeResizable(Direction direction, Region region) {
        final DragResizer resizer = new DragResizer(direction, region);

        region.setOnMousePressed(resizer::mousePressed);
        region.setOnMouseDragged(resizer::mouseDragged);
        region.setOnMouseMoved(resizer::mouseOver);
        region.setOnMouseReleased(resizer::mouseReleased);
    }

    protected void mouseReleased(MouseEvent event) {
        dragging = false;
        region.setCursor(Cursor.DEFAULT);
    }

    protected void mouseOver(MouseEvent event) {
        if (isInDraggableZone(event) || dragging) {
            if (preCursor != null) {
                preCursor = region.getCursor();
            }
            region.setCursor(Cursor.S_RESIZE);
        } else if (preCursor != null) {
            region.setCursor(preCursor);
            preCursor = null;
        }
    }

    protected boolean isInDraggableZone(MouseEvent event) {
        if (direction == Direction.TOP) {
            return event.getY() < RESIZE_MARGIN;
        } else if (direction == Direction.BOTTOM) {
            return event.getY() > (region.getHeight() - RESIZE_MARGIN);
        } else {
            throw new UnsupportedOperationException();
        }
    }

    protected void mouseDragged(MouseEvent event) {
        if (!dragging) {
            return;
        }

        double mouseY = event.getScreenY();

        double height = region.getPrefHeight() == -1 ? region.getHeight() : region.getPrefHeight();

        double newHeight = height + (y - mouseY);

        region.setPrefHeight(newHeight);

        y = mouseY;
    }

    protected void mousePressed(MouseEvent event) {
        // ignore clicks outside of the draggable margin
        if (!isInDraggableZone(event)) {
            return;
        }

        dragging = true;

        y = event.getScreenY();
    }
}