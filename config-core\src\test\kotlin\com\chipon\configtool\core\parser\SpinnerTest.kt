package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.VerticalContainer
import com.chipon.configtool.core.model.control.UIDoubleSpinner
import com.chipon.configtool.core.model.control.UISpinner
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class SpinnerTest {

    @Test
    fun `spinner basic parse`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <doublespinner suffix="%" range="0;100" step="5" default="10"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val spinner = (((root.children[0] as <PERSON><PERSON><PERSON><PERSON><PERSON>).children[0] as <PERSON><PERSON><PERSON><PERSON>)
            .children[0] as Vert<PERSON><PERSON><PERSON><PERSON>).children[0] as UIDoubleSpinner

        spinner.suffix shouldBe "%"
        spinner.range shouldBe Pair(0.0, 100.0)
        spinner.step shouldBe 5
        spinner.default shouldBe 10
    }

    @Test
    fun `spinner validation detects default out of range`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <doublespinner range="0;50" default="60"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("default value") } shouldBe true
    }

    @Test
    fun `int spinner basic parse`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <spinner range="0;100" step="5" default="10"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val spinner = (((root.children[0] as TabContainer).children[0] as GroupContainer)
            .children[0] as VerticalContainer).children[0] as UISpinner

        spinner.range shouldBe Pair(0, 100)
        spinner.step shouldBe 5
        spinner.default shouldBe 10
    }

    @Test
    fun `int spinner validation detects default out of range`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <spinner range="0;50" default="60"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("default value") } shouldBe true
    }
} 