package com.chipon.configtool.net;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelOption;
import io.netty.channel.MultiThreadIoEventLoopGroup;
import io.netty.channel.nio.NioIoHandler;
import io.netty.channel.socket.nio.NioSocketChannel;
import java.net.InetSocketAddress;

/**
 * Test only
 *
 * <AUTHOR>
 */
public class EthernetClient extends BaseClient {

    private final InetSocketAddress remoteAddress;
    private Channel channel;

    public EthernetClient(InetSocketAddress remoteAddress) {
        this.remoteAddress = remoteAddress;
    }

    @Override
    public Channel getChannel() {
        return channel;
    }

    @Override
    public Channel connect() throws Exception {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(new MultiThreadIoEventLoopGroup(NioIoHandler.newFactory()))
            .channel(NioSocketChannel.class)
            .remoteAddress(remoteAddress)
            .option(ChannelOption.TCP_NODELAY, true)
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000)
            .handler(defaultHandlers());

        channel = bootstrap.connect().sync().channel();
        return channel;
    }

}
