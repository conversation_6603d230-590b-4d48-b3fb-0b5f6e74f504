package com.chipon.configtool.app.views

import com.chipon.configtool.app.APP_NAME
import com.chipon.configtool.app.render.UIRenderer
import com.chipon.configtool.core.logTime
import com.chipon.configtool.core.model.ProfileRoot
import javafx.geometry.Pos
import tornadofx.*

/**
 * <AUTHOR>
 */

object EmptyView : View(APP_NAME) {
    override val root = label(messages["EmptyView"]) {
        alignment = Pos.CENTER
        style {
            fontSize = 25.px
        }
    }
}

class ProfileView(val profile: ProfileRoot) : View(APP_NAME) {
    override val root = scrollpane {
        padding = insets(0.0,15.0,0.0,15.0)
        logTime("Build profile") {
            UIRenderer.buildView(profile, this)
        }
        /*  After layout has settled, ask the window to resize itself */
        runLater {
//            scene.window.sizeToScene()
//            scene.window.centerOnScreen()
        }
    }
}