package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IDefaultable
import com.chipon.configtool.core.model.IValidatable
import com.chipon.configtool.core.parser.DoubleRangeDeserializer
import com.chipon.configtool.core.parser.IntRangeDeserializer
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

/**
 * <spinner>
 */
class UISpinner : BaseUIControl(), IDefaultable<Int>, IValidatable {

    var suffix: String? = null

    @JsonDeserialize(using = IntRangeDeserializer::class)
    var range: Pair<Int, Int>? = null

    val min: Int?
        get() = range?.first

    val max: Int?
        get() = range?.second

    var step: Int = 1

    override var default: Int = 0

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (range != null && min!! > max!!) {
            errors += "min (${min}) cannot be greater than max (${max})"
        }
        if (range != null && (default < min!! || default > max!!)) {
            errors += "default value $default out of range [${min}, ${max}]"
        }
        if (step <= 0) {
            errors += "step must be positive"
        }
        return errors
    }

    override fun toString(): String {
        return "spinner(" +
                "range=$range, " +
                "step=$step, " +
                "default=$default" +
                ")"
    }
}

/**
 * <doublespinner>
 */
class UIDoubleSpinner : BaseUIControl(), IDefaultable<Double>, IValidatable {

    var suffix: String? = null

    @JsonDeserialize(using = DoubleRangeDeserializer::class)
    var range: Pair<Double, Double>? = null

    val min: Double?
        get() = range?.first

    val max: Double?
        get() = range?.second

    var step: Double? = null

    override var default: Double = 0.0

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (range != null && min!! > max!!) {
            errors += "min (${min}) cannot be greater than max (${max})"
        }
        if (range != null && (default < min!! || default > max!!)) {
            errors += "default value $default out of range [${min}, ${max}]"
        }
        if (step != null && step!! <= 0) {
            errors += "step must be positive"
        }
        return errors
    }
}
