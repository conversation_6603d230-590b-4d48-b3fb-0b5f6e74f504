package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.VerticalContainer
import com.chipon.configtool.core.model.control.UIRadio
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class RadioTest {

    @Test
    fun `radio basic parsing`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Options">
                        <vbox>
                            <radio id="CTRL.WD_CTRL[5]" default="1">
                                <radiobutton label="OptA"/>
                                <radiobutton label="OptB"/>
                            </radio>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val radio = (((root.children[0] as <PERSON><PERSON><PERSON><PERSON><PERSON>).children[0] as <PERSON><PERSON><PERSON><PERSON>)
            .children[0] as VerticalContaine<PERSON>).children[0] as UIRadio

        radio.id shouldBe "CTRL.WD_CTRL[5]"
        radio.default shouldBe 1
        radio.radioButtons.size shouldBe 2
        radio.radioButtons[0].label shouldBe "OptA"
    }

    @Test
    fun `radio validation detects no buttons`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Options">
                        <vbox>
                            <radio default="0" />
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("no <radiobutton> elements") } shouldBe true
    }

    @Test
    fun `radio validation detects default index out of range`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Options">
                        <vbox>
                            <radio default="3">
                                <radiobutton label="A"/>
                                <radiobutton label="B"/>
                            </radio>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("default index out of range") } shouldBe true
    }

    @Test
    fun `radio buttons should auto-assign sequential indices`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab>
                    <group>
                        <vbox>
                            <radio default="2">
                                <radiobutton label="A"/>
                                <radiobutton label="B"/>
                                <radiobutton label="C"/>
                            </radio>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        // Trigger validation to perform normalization
        root.validate()

        val radio = (((root.children[0] as TabContainer).children[0] as GroupContainer)
            .children[0] as VerticalContainer).children[0] as UIRadio

        radio.radioButtons[0].value shouldBe 0
        radio.radioButtons[1].value shouldBe 1
        radio.radioButtons[2].value shouldBe 2
    }
} 