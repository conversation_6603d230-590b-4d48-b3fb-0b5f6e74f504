package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.BaseElement

/**
 * <AUTHOR>
 */
class UnknownPropertyException : RuntimeException {

    constructor(property: String) : super("Unknown Property $property")

    constructor(property: String, e: Exception) : super("Unknown Property $property", e)
}

class TypeMissMatchException : RuntimeException {

    constructor(property: BaseElement, e: Exception) : super("Failed to evaluate $property cause by: ${e.message}", e)
}