package com.chipon.configtool.app.render

import com.chipon.configtool.core.model.element.DataType

object DataTypeValidator {

    fun validate(value: Any, dataType: DataType): Number {
        return when (dataType) {
            DataType.int8 -> (value as Int).also { require(it in Byte.MIN_VALUE..Byte.MAX_VALUE) { "Value $it out of int8 range" } }
            DataType.uint8 -> (value as Int).also { require(it in 0..255) { "Value $it out of uint8 range" } }
            DataType.int16 -> (value as Int).also { require(it in Short.MIN_VALUE..Short.MAX_VALUE) { "Value $it out of int16 range" } }
            DataType.uint16 -> (value as Int).also { require(it in 0..65535) { "Value $it out of uint16 range" } }
            DataType.int32 -> (value as Int).also { require(it in Int.MIN_VALUE..Int.MAX_VALUE) { "Value $it out of uint16 range" } }
            DataType.uint32 -> (value as Long).also { require(it in 0..0xFFFFFFFFL) { "Value $it out of uint32 range" } }
            DataType.int64, DataType.uint64 -> value as Long
            DataType.double -> value as Double
        }
    }

    fun isValidForType(value: Number, dataType: DataType): Boolean {
        return try {
            validate(value, dataType)
            true
        } catch (_: IllegalArgumentException) {
            false
        } catch (_: ClassCastException) {
            false
        }
    }
}
