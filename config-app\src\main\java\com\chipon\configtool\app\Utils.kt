package com.chipon.configtool.app

import atlantafx.base.theme.CupertinoLight
import com.chipon.configtool.app.model.AppConfig
import com.chipon.configtool.net.transport.JSerialCommChannelConfig
import com.chipon.configtool.net.transport.JSerialCommChannelConfig.Paritybit
import com.chipon.configtool.net.transport.JSerialCommChannelConfig.Stopbits
import com.fazecast.jSerialComm.SerialPort
import javafx.application.Application.setUserAgentStylesheet
import org.slf4j.Logger
import java.util.function.Consumer

/**
 * <AUTHOR> Wu
 */

fun applyTheme(value: String?) {
    when (value) {
        "默认" -> setUserAgentStylesheet(null)
        "现代" -> setUserAgentStylesheet(CupertinoLight().userAgentStylesheet)
    }
}

fun Logger.showError(message: String, e: Exception? = null) {
    if (e == null) error(message) else error(message, e)
    tornadofx.error(null, message)
}

fun getSerialPorts(): List<SerialPort> {
    return SerialPort.getCommPorts()
        .sortedBy { it.systemPortName }
}

val serialConfiger = Consumer<JSerialCommChannelConfig> {
    it.baudrate = AppConfig.serialBaudRate.value
    it.databits = AppConfig.serialDataBit.value
    it.stopbits = when (AppConfig.serialStopBit.value) {
        1.0 -> Stopbits.STOPBITS_1
        1.5 -> Stopbits.STOPBITS_1_5
        2.0 -> Stopbits.STOPBITS_2
        else -> Stopbits.STOPBITS_1
    }
    it.paritybit = when (AppConfig.serialParity.value) {
        "无" -> Paritybit.NONE
        "偶" -> Paritybit.EVEN
        "奇" -> Paritybit.ODD
        "标志" -> Paritybit.MARK
        "空格" -> Paritybit.SPACE
        else -> Paritybit.NONE
    }
}

fun String.toIntHex() = hexToInt(hexFormat)
fun String.toLongHex() = hexToLong(hexFormat)
