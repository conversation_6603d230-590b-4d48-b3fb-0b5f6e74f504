<?xml version="1.0" encoding="UTF-8"?>
<uicfg version="2.0.3">

    <!-- ========= Variables used in the demo ========= -->
    <tab label="Main Demo">
        <hbox>
            <!-- addr 0xf1-->
            <var id="VAR.A0" type="uint64"/>
            <!-- addr 0xf2-->
            <var id="VAR.B0" type="uint64"/>
            <var id="VAR.SUM"/>
            <var id="GRID.VALUE"/>

            <!-- ========= A math element (adds the two vars) ========= -->
            <math id="MATH.SUM" visible="false" type="uint64" formula="VAR.A0+VAR.B0">
                <action event="changed" cmd="setState" data="VAR.SUM=MATH.SUM"/>
            </math>

            <!-- ========= Periodic Ping (CMD 0x31) ========= -->
            <timer id="TIMER_PING" interval="1000" oneshot="0" run="1">
                <!-- emits AA seq 31 00 checksum 55  (payload byte is 0x00) -->
                <action event="changed" cmd="sendUSB" data="p:0x00"/>
            </timer>

            <!-- ========= Manual controls ========= -->
            <group label="Controls">
                <vbox>
                    <vbox>
                        <hbox>
                            <!-- Write single address (CMD 0x41, one address) -->
                            <button label="Write A0">
                                <!-- LEN= (addr 2B + size 1B + data 1B) = 4 → 00 04 -->
                                <action event="clicked" cmd="sendUSB" data="w:[0xf1:VAR.A0]"/>
                            </button>

                            <button label="Write B0">
                                <!-- LEN= (addr 2B + size 1B + data 1B) = 4 → 00 04 -->
                                <action event="clicked" cmd="sendUSB" data="w:[0xf2:VAR.B0]"/>
                            </button>
                            <!-- Write multi address (two addresses) -->
                            <button label="Write A0=0x34, B0=0xffffffff">
                                <!-- LEN =  (2+1+1) + (2+1+8) = 9 → 00 09 -->
                                <action event="clicked" cmd="sendUSB"
                                        data="0x41;0x00;0x0F;0x00;0xF1;0x01;0x34;0x00;0xF2;0x08;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF"/>
                            </button>
                        </hbox>
                        <hbox>
                            <!-- Read single address -->
                            <button label="Read A0">
                                <!-- LEN = 2 → 00 02 -->
                                <action event="clicked" cmd="sendUSB" data="r:[0xf1]"/>
                            </button>

                            <!-- Read multi address -->
                            <button label="Read A0 B0">
                                <!-- LEN = 4 → 00 04 -->
                                <action event="clicked" cmd="sendUSB" data="r:[0xf1][0xf2]"/>
                            </button>
                        </hbox>

                        <hbox>
                            <hbox>
                                <text label="A0 = "/>
                                <math formula="VAR.A0" color="#1bff44"/>
                            </hbox>
                            <hbox>
                                <text label="B0 = "/>
                                <math type="uint64" formula="VAR.B0" color="#ff5022"/>
                            </hbox>
                            <hbox>
                                <text label="A0 + B0 = "/>
                                <math formula="VAR.SUM"/>
                            </hbox>
                        </hbox>

                    </vbox>

                    <!-- Manual Ping with custom byte -->
                    <hbox>
                        <text label="Ping Payload:"/>
                        <!-- value saved in VAR.A0 for reuse -->
                        <spinner id="PING.DATA" range="0;255" step="1" default="1"/>
                        <button label="Send Ping">
                            <action event="clicked" cmd="sendUSB" data="0x31;PING.DATA"/>
                        </button>
                    </hbox>

                    <hline/>
                    <!-- Combo & checkbox bound to VARs -->
                    <hbox>
                        <text label="A0:"/>
                        <combo id="COMBO.A0">
                            <item label="0x00" value="0"/>
                            <item label="0x11" value="17"/>
                            <item label="0x22" value="34"/>
                            <item label="0x33" value="51"/>
                        </combo>

                        <vline/>
                        <button label="ADD B0">
                            <action event="clicked" cmd="setState" data="{VAR.B0+=1}"/>
                        </button>
                    </hbox>
                </vbox>
            </group>

            <!-- ========= Component Binding Demo ========= -->
            <group label="Component Binding">
                <vbox>
                    <!-- Spinner with binding -->
                    <hbox>
                        <text label="Spinner Value:"/>
                        <spinner id="SPINNER.VALUE" range="0;100" default="50"/>
                        <text label="Bound Label:"/>
                        <math formula="SPINNER.VALUE" color="#1bff44"/>
                    </hbox>

                    <!-- Radio group with binding -->
                    <hbox>
                        <text label="Radio Selection:"/>
                        <radio id="RADIO.MODE">
                            <radiobutton label="Mode 1" value="1"/>
                            <radiobutton label="Mode 2" value="2"/>
                            <radiobutton label="Mode 3" value="3"/>
                        </radio>
                        <text label="Selected:"/>
                        <math formula="RADIO.MODE" color="#ff5022"/>
                    </hbox>

                    <!-- Toggle button with binding -->
                    <hbox>
                        <text label="Toggle State:"/>
                        <togglebutton id="TOGGLE.STATE" label="Toggle Me"/>
                        <text label="State:"/>
                        <math formula="TOGGLE.STATE" color="#1b44ff"/>
                    </hbox>

                    <!-- Checkbox with binding -->
                    <hbox>
                        <checkbox id="CHECK.STATE" label="Check Me"/>
                        <text label="Checked:"/>
                        <math formula="CHECK.STATE" color="#ff1b44"/>
                    </hbox>
                </vbox>
            </group>

            <!-- ========= Lock On Demo ========= -->
            <group label="Lock On Features">
                <vbox>
                    <!-- Checkbox with lock on -->
                    <hbox>
                        <checkbox id="LOCK.CHECKBOX" label="Lock Controls"
                                lockon="LOCK.COMBO=2;LOCK.SPINNER=75;LOCK.RADIO=2;LOCK.TOGGLE=1"/>
                        <text label="Lock State:"/>
                        <math formula="LOCK.CHECKBOX" color="#ffaa00"/>
                    </hbox>

                    <!-- Combo with lock on items -->
                    <hbox>
                        <text label="Lock Combo:"/>
                        <combo id="LOCK.COMBO">
                            <item label="Option 0" value="0"/>
                            <item label="Option 1" value="1"/>
                            <item label="Option 2" value="2"/>
                            <item label="Option 3" value="3"/>
                        </combo>
                        <text label="Value:"/>
                        <math formula="LOCK.COMBO" color="#aa00ff"/>
                    </hbox>

                    <!-- Spinner with lock on -->
                    <hbox>
                        <text label="Lock Spinner:"/>
                        <spinner id="LOCK.SPINNER" range="0;100" default="0"/>
                        <text label="Value:"/>
                        <math formula="LOCK.SPINNER" color="#00ffaa"/>
                    </hbox>

                    <!-- Radio with lock on -->
                    <hbox>
                        <text label="Lock Radio:"/>
                        <radio id="LOCK.RADIO">
                            <radiobutton label="Radio 0" value="0"/>
                            <radiobutton label="Radio 1" value="1"/>
                            <radiobutton label="Radio 2" value="2"/>
                        </radio>
                        <text label="Selected:"/>
                        <math formula="LOCK.RADIO" color="#ff00aa"/>
                    </hbox>

                    <!-- Toggle with lock on -->
                    <hbox>
                        <text label="Lock Toggle:"/>
                        <togglebutton id="LOCK.TOGGLE" label="Lock Toggle"/>
                        <text label="State:"/>
                        <math formula="LOCK.TOGGLE" color="#aaff00"/>
                    </hbox>
                </vbox>
            </group>

            <!-- ========= Additional Components Demo ========= -->
            <group label="Additional Components">
                <vbox>
                    <!-- LED component -->
                    <hbox>
                        <text label="LED Demo:"/>
                        <checkbox id="LED.CHECK" label="Toggle LED"/>
                        <led data="LED.CHECK" bitmask="0x01" oncolor="green" offcolor="red"
                                size="20;20"/>
                    </hbox>

                    <!-- Progress bar -->
                    <hbox>
                        <text label="Progress:"/>
                        <progressbar id="PROGRESS.BAR" default="0.3"/>
                        <text label="Value:"/>
                        <math formula="PROGRESS.BAR" color="#00aaff"/>
                    </hbox>

                    <!-- Text input -->
                    <hbox>
                        <text label="Text Input:"/>
                        <textinput id="TEXT.INPUT"/>
                        <text label="Length:"/>
                        <math formula="TEXT.INPUT.length()" color="#ffaa00"/>
                    </hbox>

                    <!-- Double spinner -->
                    <hbox>
                        <text label="Double Spinner:"/>
                        <doublespinner id="DOUBLE.SPINNER" range="0.0;10.0" step="0.1"
                                default="5.5"/>
                        <text label="Value:"/>
                        <math formula="DOUBLE.SPINNER" color="#aa00ff"/>
                    </hbox>

                    <!-- Switch component -->
                    <hbox>
                        <text label="Switch Demo:"/>
                        <switch id="SWITCH.DEMO" label="Toggle Switch" default="0"/>
                        <text label="State:"/>
                        <math formula="SWITCH.DEMO" color="#ff8800"/>
                    </hbox>
                </vbox>
            </group>
        </hbox>
    </tab>

    <!-- ========= Second Tab Demo ========= -->
    <tab label="Advanced Features">
        <vbox>
            <text label="This tab demonstrates advanced features"/>
        </vbox>
    </tab>

</uicfg>