<?xml version="1.0" encoding="UTF-8"?>
<uicfg version="2.0.3">

    <!-- ========= Variables used in the demo ========= -->
    <hbox>
        <!-- addr 0xf2-->
        <var id="VAR.B0" type="uint64"/>
        <var id="VAR.SUM"/>

        <!-- ========= A math element (adds the two vars) ========= -->
        <math id="MATH.SUM" visible="false" type="uint64" formula="VAR.A0+VAR.B0">
            <action event="changed" cmd="setState" data="VAR.SUM=MATH.SUM"/>
        </math>

        <!-- ========= Periodic Ping (CMD 0x31) ========= -->
        <timer id="TIMER_PING" interval="1000" oneshot="0" run="1">
            <!-- emits AA seq 31 00 checksum 55  (payload byte is 0x00) -->
            <action event="changed" cmd="sendUSB" data="0x31;0x00"/>
        </timer>

        <!-- ========= Manual controls ========= -->
        <group label="Controls">
            <vbox>
                <vbox>
                    <hbox>
                        <!-- Write single address (CMD 0x41, one address) -->
                        <button label="Write A0">
                            <!-- LEN= (addr 2B + size 1B + data 1B) = 4 → 00 04 -->
                            <action event="clicked" cmd="sendUSB" data="w:[0xf1:VAR.A0]"/>
                        </button>

                        <button label="Write B0">
                            <!-- LEN= (addr 2B + size 1B + data 1B) = 4 → 00 04 -->
                            <action event="clicked" cmd="sendUSB" data="w:[0xf2:VAR.B0]"/>
                        </button>
                        <!-- Write multi address (two addresses) -->
                        <button label="Write A0=0x34, B0=0xffffffff">
                            <!-- LEN =  (2+1+1) + (2+1+8) = 9 → 00 09 -->
                            <action event="clicked" cmd="sendUSB"
                                    data="0x41;0x00;0x0F;0x00;0xF1;0x01;0x34;0x00;0xF2;0x08;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF;0xFF"/>
                        </button>
                    </hbox>
                    <hbox>
                        <!-- Read single address -->
                        <button label="Read A0">
                            <!-- LEN = 2 → 00 02 -->
                            <action event="clicked" cmd="sendUSB" data="r:[0xf1]"/>
                        </button>

                        <!-- Read multi address -->
                        <button label="Read A0 B0">
                            <!-- LEN = 4 → 00 04 -->
                            <action event="clicked" cmd="sendUSB" data="r:[0xf1][0xf2]"/>
                        </button>
                    </hbox>
                    <hbox>
                        <hbox>
                            <text label="A0 = "/>
                            <math formula="VAR.A0" color="#1bff44"/>
                        </hbox>
                        <hbox>
                            <text label="B0 = "/>
                            <math type="uint64" formula="VAR.B0" color="#ff5022"/>
                        </hbox>
                        <hbox>
                            <text label="A0 + B0 = "/>
                            <math formula="VAR.SUM"/>
                        </hbox>
                    </hbox>
                </vbox>

                <!-- Manual Ping with custom byte -->
                <hbox>
                    <text label="Ping Payload:"/>
                    <!-- value saved in VAR.A0 for reuse -->
                    <spinner id="PING.DATA" range="0;255" step="1" default="1"/>
                    <button label="Send Ping">
                        <action event="clicked" cmd="sendUSB" data="0x31;PING.DATA"/>
                    </button>
                </hbox>

                <hline/>
                <!-- Combo & checkbox bound to VARs -->
                <hbox>
                    <text label="A0:"/>
                    <combo id="VAR.A0">
                        <item label="0x00" value="0"/>
                        <item label="0x11" value="17"/>
                        <item label="0x22" value="34"/>
                        <item label="0x33" value="51"/>
                    </combo>

                    <vline/>
                    <button label="ADD B0">
                        <action event="clicked" cmd="setState" data="{VAR.B0+=1}"/>
                    </button>
                </hbox>
            </vbox>
        </group>
    </hbox>

</uicfg>