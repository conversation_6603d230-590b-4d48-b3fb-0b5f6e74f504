package com.chipon.configtool.app

import com.chipon.configtool.app.render.Evaluator
import org.apache.commons.jexl3.JexlContext
import org.apache.commons.jexl3.JexlEngine
import org.apache.commons.jexl3.MapContext
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

/**
 * <AUTHOR> Wu
 */
class EvaluatorTest {

    val jexl: JexlEngine = Evaluator.jexl

    @Test
    fun `test evaluate`() {
        val expression = "((UIO.ADC/1000)*2.33)+0.7"
        val map: Map<String, Int> = mutableMapOf(
            "UIO.ADC" to 1000
        )
        val context: JexlContext = MapContext(map)
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Result: $result")
        // 根据实际结果设置断言
        assertEquals(3.03, result as Double, 1e-6)
    }

    @Test
    fun `test bit manipulation formula`() {
        // 测试类似 (CTRL.M_S_CTRL[7:6]*0x40+CTRL.M_S_CTRL[4:3]*0x08+CTRL.M_S_CTRL[2]*0x04+CTRL.M_S_CTRL[1:0]) 的表达式
        val expression = "(CTRL.M_S_CTRL_7_6*0x40+CTRL.M_S_CTRL_4_3*0x08+CTRL.M_S_CTRL_2*0x04+CTRL.M_S_CTRL_1_0)"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.M_S_CTRL_7_6" to 2,  // bits [7:6] = 2
                "CTRL.M_S_CTRL_4_3" to 1,  // bits [4:3] = 1
                "CTRL.M_S_CTRL_2" to 1,    // bit [2] = 1
                "CTRL.M_S_CTRL_1_0" to 3   // bits [1:0] = 3
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Bit manipulation result: $result")
        // 2*64 + 1*8 + 1*4 + 3 = 128 + 8 + 4 + 3 = 143
        assertEquals(143, result as Int)
    }

    @Test
    fun `test conditional ternary expression`() {
        // 测试类似 (CTRL.WD_CTRL[5]+CTRL.WD_CTRL[4]+1).*1 ? 0x80+CTRL.WD_CTRL[5]*0x20+CTRL.WD_CTRL[4]*0x10+4 : CTRL.WD_CTRL[5]*0x20+CTRL.WD_CTRL[4]*0x10+4 的表达式
        val expression = "(CTRL.WD_CTRL_5+CTRL.WD_CTRL_4+1)*1 ? 0x80+CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4 : CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.WD_CTRL_5" to 1,
                "CTRL.WD_CTRL_4" to 0
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Conditional ternary result: $result")
        // (1+0+1)*1 = 2 (true), 所以返回 0x80+1*0x20+0*0x10+4 = 128+32+0+4 = 164
        assertEquals(164, result as Int)
    }

    @Test
    fun `test nested conditional expression`() {
        // 测试类似 CTRL.SWK_CDR_CTRL2[1:0] == 3 ? 10 : (CTRL.SWK_CDR_CTRL2[1:0] == 2 ? 20 : (CTRL.SWK_CDR_CTRL2[1:0] == 1 ? 40 : 80)) 的表达式
        val expression = "CTRL.SWK_CDR_CTRL2_1_0 == 3 ? 10 : (CTRL.SWK_CDR_CTRL2_1_0 == 2 ? 20 : (CTRL.SWK_CDR_CTRL2_1_0 == 1 ? 40 : 80))"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.SWK_CDR_CTRL2_1_0" to 2
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Nested conditional result: $result")
        // CTRL.SWK_CDR_CTRL2_1_0 == 2, 所以返回 20
        assertEquals(20, result as Int)
    }

    @Test
    fun `test complex boolean expression`() {
        // 测试类似 ((CTRL.M_S_CTRL.*0xC0)==0x00)*CTRL.FAM_PROD_STAT?1:((CTRL.FAM_PROD_STAT==0)?2:((CTRL.M_S_CTRL.*0xC0)==0x80?4:8)) 的表达式
        val expression = "((CTRL.M_S_CTRL*0xC0)==0x00)*CTRL.FAM_PROD_STAT?1:((CTRL.FAM_PROD_STAT==0)?2:((CTRL.M_S_CTRL*0xC0)==0x80?4:8))"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.M_S_CTRL" to 0,
                "CTRL.FAM_PROD_STAT" to 84  // 0x54
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Complex boolean result: $result")
        // ((0*0xC0)==0x00) = true, true*84 = 84, 84 ? 1 : ... = 1
        assertEquals(1, result as Int)
    }

    @Test
    fun `test arithmetic with multiplication and addition`() {
        // 测试类似 CTRL.PWM_DC*2.55+0.1 的表达式
        val expression = "CTRL.PWM_DC*2.55+0.1"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.PWM_DC" to 50.0
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Arithmetic result: $result")
        // 50*2.55+0.1 = 127.5+0.1 = 127.6
        assertEquals(127.6, result as Double, 1e-6)
    }

    @Test
    fun `test boolean comparison expression`() {
        // 测试类似 (USB.ERROR_CODE==0x11)?1:0 的表达式
        val expression = "(USB.ERROR_CODE==0x11)?1:0"
        val context: JexlContext = MapContext(
            mapOf(
                "USB.ERROR_CODE" to 17  // 0x11
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Boolean comparison result: $result")
        // USB.ERROR_CODE == 17 (0x11) 为真，返回 1
        assertEquals(1, result as Int)
    }

    @Test
    fun `test range validation expression`() {
        // 测试类似 (CTRL.SWK_IDx_CTRL > 0x1FFFFFFF) ? 0 : 1 的表达式
        val expression = "(CTRL.SWK_IDx_CTRL > 0x1FFFFFFF) ? 0 : 1"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.SWK_IDx_CTRL" to 536870911  // 0x1FFFFFFF
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Range validation result: $result")
        // 536870911 == 0x1FFFFFFF, 不大于，所以返回 1
        assertEquals(1, result as Int)
    }

    @Test
    fun `test simple variable reference`() {
        // 测试类似 UIO.VERSION 的简单变量引用
        val expression = "UIO.VERSION"
        val context: JexlContext = MapContext(
            mapOf(
                "UIO.VERSION" to 42
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Simple variable result: $result")
        // 直接返回变量值 42
        assertEquals(42, result as Int)
    }

    @Test
    fun `test complex bit operations with format`() {
        // 测试类似 CTRL.HW_CTRL_2[7:5]*0x20+CTRL.HW_CTRL_2[4]*0x10+CTRL.HW_CTRL_2[3:2]*4 的表达式
        val expression = "CTRL.HW_CTRL_2_7_5*0x20+CTRL.HW_CTRL_2_4*0x10+CTRL.HW_CTRL_2_3_2*4"
        val context: JexlContext = MapContext(
            mapOf(
                "CTRL.HW_CTRL_2_7_5" to 3,  // bits [7:5] = 3
                "CTRL.HW_CTRL_2_4" to 1,    // bit [4] = 1
                "CTRL.HW_CTRL_2_3_2" to 2   // bits [3:2] = 2
            )
        )
        val jexlExpression = jexl.createExpression(expression)
        val result = jexlExpression.evaluate(context)
        println("Complex bit operations result: $result")
        // 3*32 + 1*16 + 2*4 = 96 + 16 + 8 = 120
        assertEquals(120, result as Int)
    }

    @Test
    fun `test extract variables from simple expression`() {
        val expression = "UIO.VERSION"
        val variables = Evaluator.computeVariables(expression)
        assertEquals(setOf("UIO.VERSION"), variables)
    }

    @Test
    fun `test extract variables from arithmetic expression`() {
        val expression = "((UIO.ADC/1000)*2.33)+0.7"
        val variables = Evaluator.computeVariables(expression)
        assertEquals(setOf("UIO.ADC"), variables)
    }

    @Test
    fun `test extract variables from complex bit manipulation`() {
        val expression = "(CTRL.M_S_CTRL_7_6*0x40+CTRL.M_S_CTRL_4_3*0x08+CTRL.M_S_CTRL_2*0x04+CTRL.M_S_CTRL_1_0)"
        val variables = Evaluator.computeVariables(expression)

        assertEquals(
            setOf(
                "CTRL.M_S_CTRL_7_6",
                "CTRL.M_S_CTRL_4_3",
                "CTRL.M_S_CTRL_2",
                "CTRL.M_S_CTRL_1_0"
            ), variables.toSet()
        )
    }

    @Test
    fun `test extract variables from conditional expression`() {
        val expression = "(CTRL.WD_CTRL_5+CTRL.WD_CTRL_4+1)*1 ? 0x80+CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4 : CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4"
        val variables = Evaluator.computeVariables(expression)

        val expectedVariables = setOf(
            "CTRL.WD_CTRL_5",
            "CTRL.WD_CTRL_4"
        )
        assertEquals(expectedVariables, variables.toSet())
    }

    @Test
    fun `test extract variables from nested conditional expression`() {
        val expression = "CTRL.SWK_CDR_CTRL2_1_0 == 3 ? 10 : (CTRL.SWK_CDR_CTRL2_1_0 == 2 ? 20 : (CTRL.SWK_CDR_CTRL2_1_0 == 1 ? 40 : 80))"
        val variables = Evaluator.computeVariables(expression)
        assertEquals(setOf("CTRL.SWK_CDR_CTRL2_1_0"), variables)
    }

    @Test
    fun `test extract variables from complex boolean expression`() {
        val expression = "((CTRL.M_S_CTRL*0xC0)==0x00)*CTRL.FAM_PROD_STAT?1:((CTRL.FAM_PROD_STAT==0)?2:((CTRL.M_S_CTRL*0xC0)==0x80?4:8))"
        val variables = Evaluator.computeVariables(expression)
        
        val expectedVariables = setOf(
            "CTRL.M_S_CTRL",
            "CTRL.FAM_PROD_STAT"
        )
        assertEquals(expectedVariables, variables.toSet())
    }

    @Test
    fun `test extract variables from expression with multiple namespaces`() {
        val expression = "USB.ERROR_CODE + CTRL.PWM_DC + UIO.VERSION"
        val variables = Evaluator.computeVariables(expression)
        
        val expectedVariables = setOf(
            "USB.ERROR_CODE",
            "CTRL.PWM_DC", 
            "UIO.VERSION"
        )
        assertEquals(expectedVariables, variables.toSet())
    }

    @Test
    fun `test extract variables from empty or invalid expression`() {
        // 空表达式服务
        assertEquals(emptySet<String>(), Evaluator.computeVariables(""))
        assertEquals(emptySet<String>(), Evaluator.computeVariables("   "))
        
        // 只有常量的表达式
        assertEquals(emptySet<String>(), Evaluator.computeVariables("123"))
        assertEquals(emptySet<String>(), Evaluator.computeVariables("0x10"))
        assertEquals(emptySet<String>(), Evaluator.computeVariables("true"))
        assertEquals(emptySet<String>(), Evaluator.computeVariables("(1+2)*3"))
    }

    @Test
    fun `test extract variables with range validation`() {
        val expression = "(CTRL.SWK_IDx_CTRL > 0x1FFFFFFF) ? 0 : 1"
        val variables = Evaluator.computeVariables(expression)
        assertEquals(setOf("CTRL.SWK_IDx_CTRL"), variables)
    }

}