plugins {
    id("org.openjfx.javafxplugin") version "0.1.0"
    application
//    id("org.graalvm.buildtools.native") version "0.10.6"
}

application {
    mainClass = "com.chipon.configtool.app.App"
}

dependencies {
    implementation(project(":config-core"))
    implementation(project(":config-net"))

    implementation(libs.kotlinxCoroutines)
    implementation(libs.tornadofx) {
        isChanging = true
    }
    implementation(libs.atlantafx)
    implementation(libs.controlsfx)
    implementation("org.kordamp.ikonli:ikonli-materialdesign2-pack:12.4.0")
    implementation("org.kordamp.ikonli:ikonli-javafx:12.4.0")
    implementation("org.apache.commons:commons-jexl3:3.5.0")
    implementation("org.fxmisc.richtext:richtextfx:0.11.5")

    //test
    testImplementation(libs.kotest)
    testImplementation(kotlin("test"))
    testImplementation("org.testfx:testfx-core:4.0.18")
    testImplementation("org.testfx:testfx-junit5:4.0.18")
    testImplementation("org.testfx:openjfx-monocle:21.0.2")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.+")
}

javafx {
    version = "21.0.7"
    modules = listOf("javafx.controls", "javafx.graphics", "javafx.fxml")
    configurations = arrayOf("implementation", "testImplementation")
}

//graalvmNative {
//    binaries {
//        named("main") {
//            imageName.set("hexon")
//            mainClass.set("com.chipon.configtool.app.AppKt")
//
//            verbose.set(true)
//            fallback.set(true)
//            sharedLibrary.set(false)
//            richOutput.set(false)
//            quickBuild.set(false)
//
////            buildArgs.add("--link-at-build-time")
//        }
//    }
//}
