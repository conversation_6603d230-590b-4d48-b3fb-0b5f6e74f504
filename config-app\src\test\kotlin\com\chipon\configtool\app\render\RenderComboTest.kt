package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.scene.Scene
import javafx.scene.control.ComboBox
import javafx.scene.control.Label
import javafx.scene.control.ScrollPane
import javafx.scene.layout.VBox
import javafx.stage.Stage
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit

/**
 * Verify that when a ComboBox selection changes, the bound Math label updates accordingly.
 */
class RenderComboTest : TestCase() {

    @Test
    fun `label should update when combo selection changes`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <vbox>
                    <combo id="COMBO_MODE" default="8">
                        <item label="Option0" value="2"/>
                        <item label="Option1" value="1"/>
                        <item label="Option2" value="8"/>
                    </combo>
                    <math formula="COMBO_MODE"/>
                </vbox>
            </uicfg>
        """.trimIndent()

        val profile = XmlConfigParser.parseFromString(xmlContent)

        setUpFixture { root ->
            // Build UI from profile
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)
            val combo = content.children[0] as ComboBox<*>
            val label = content.children[1] as Label

            combo.selectionModel.selectedIndex shouldBe 2
            label.text shouldBe "8"

            // Initial selection should be 0
            combo.selectionModel.select(0)
            label.text shouldBe "2"

            // Change to index 2
            combo.selectionModel.select(2)
            label.text shouldBe "8"

            // Change to index 1
            combo.selectionModel.select(1)
            label.text shouldBe "1"
        }
    }
}