pluginManagement {
    repositories {
        maven {
            url = uri("http://192.168.110.210/nexus/repository/maven-public/")
            isAllowInsecureProtocol = true
        }
    }
}

dependencyResolutionManagement {

    @Suppress("UnstableApiUsage")
    repositories {
        maven {
            url = uri("https://maven.aliyun.com/repository/public/")
        }
        maven {
            url = uri("http://192.168.110.210/nexus/repository/maven-public/")
            isAllowInsecureProtocol = true
        }
    }
}

plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.8.0"
}

include(":config-core")
include(":config-net")
include(":config-app")
include(":test-report")

rootProject.name = "config_tool"