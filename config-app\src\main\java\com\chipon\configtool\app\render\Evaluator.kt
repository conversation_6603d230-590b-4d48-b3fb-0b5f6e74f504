package com.chipon.configtool.app.render

import javafx.beans.binding.NumberExpression
import javafx.beans.property.DoubleProperty
import javafx.beans.property.IntegerProperty
import javafx.beans.property.LongProperty
import javafx.beans.property.Property
import org.apache.commons.jexl3.JexlBuilder
import org.apache.commons.jexl3.JexlContext
import org.apache.commons.jexl3.JexlEngine

object Evaluator {

    val jexl: JexlEngine = JexlBuilder()
        .silent(false)
        .strict(true)
        .safe(false)
        .debug(true)
        .create()

    fun evaluate(expression: String, contexts: Map<String, BaseContext> = emptyMap()): Any {
        val jexlContext = MyJexlContext(contexts)
        val script = jexl.createScript(expression)
        return script.execute(jexlContext)
    }

    /**
     * script.variables 返回 Set<List<String>>，每个List<String>代表一个变量的路径
     * 例如: CTRL.M_S_CTRL 会被表示为 ["CTRL", "M_S_CTRL"]
     */
    fun computeVariables(expression: String): Set<String> {
        val script = jexl.createScript(expression)
        return script.variables.mapTo(LinkedHashSet()) { it.joinToString(".") }
    }

    /**
     * Parse dependencies and resolved them.
     */
    fun computeDependencies(expression: String, contexts: Map<String, BaseContext>): Array<NumberExpression> {
        val variables = computeVariables(expression)
        val map = variables.map {
            contexts[it] ?: throw ContextNotFoundException(it)
        }.map {
            if (!it.isResolved()) {
                it.resolve(contexts)
            }
            it.valueProperty
        }.toTypedArray()
        return map
    }

    class MyJexlContext(val contexts: Map<String, BaseContext>) : JexlContext {

        override fun get(name: String): Any? {
            val context: BaseContext = contexts[name] ?: return null
            return context.value
        }

        override fun has(name: String): Boolean {
            return contexts.containsKey(name)
        }

        override fun set(name: String, value: Any?) {
            val ctx = contexts[name] ?: throw IllegalArgumentException("Unknown context id: $name")

            val prop = ctx.valueProperty
            when (prop) {
                is IntegerProperty -> prop.value = (value as Number).toInt()
                is LongProperty -> prop.value = (value as Number).toLong()
                is DoubleProperty -> prop.value = (value as Number).toDouble()
                is Property<*> -> {
                    @Suppress("UNCHECKED_CAST")
                    (prop as Property<Any?>).value = value
                }
                else -> throw UnsupportedOperationException("Context value is not writable: $name")
            }
        }
    }
}
