package com.chipon.configtool.core

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.time.DurationUnit
import kotlin.time.TimedValue
import kotlin.time.measureTimedValue


/**
 * <AUTHOR>
 */

val logger: Logger = LoggerFactory.getLogger("default")

/**
 * measure and log the execution time of a code block.
 */
inline fun <T> logTime(message: String, block: () -> T): T {
    val timedValue: TimedValue<T> = measureTimedValue {
        block()
    }
    logger.info("$message takes ${timedValue.duration.toString(DurationUnit.MILLISECONDS)}")
    return timedValue.value
}
