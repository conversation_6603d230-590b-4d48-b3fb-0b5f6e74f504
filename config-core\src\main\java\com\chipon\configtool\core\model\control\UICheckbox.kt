package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.element.Action
import com.chipon.configtool.core.parser.LockerDeserialize
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <checkbox>
 */
open class UICheckbox : BaseUIControl(), ILocker, IAction, IDisplayable, IDefaultable<Int> {

    override lateinit var label: String

    override var default: Int = 0

    @JsonDeserialize(using = LockerDeserialize::class)
    @JacksonXmlProperty(isAttribute = true)
    override var lockon: LockOn? = null

    @JacksonXmlProperty(localName = "action")
    override var actions: List<Action> = emptyList()

    override fun toString(): String {
        return "checkbox(" +
                "label=$label, " +
                "default=$default, " +
                "lockon=$lockon, " +
                "actions=$actions" +
                ")"
    }
}

/**
 * <switch>, same with Checkbox but with different UI
 */
class UISwitch : UICheckbox(), IColorable {
    override var color: String? = null
}

/**
 * <togglebutton>, same with Checkbox
 */
class UIToggle : UICheckbox(), IColorable {
    override var color: String? = null
}