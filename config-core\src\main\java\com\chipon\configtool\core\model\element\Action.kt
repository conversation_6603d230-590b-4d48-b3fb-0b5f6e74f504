package com.chipon.configtool.core.model.element

import com.chipon.configtool.core.model.IValidatable

/**
 * <action>
 */
class Action : IValidatable {

    lateinit var event: Event

    lateinit var cmd: Cmd

    /**
     * data 字段含义：
     * - 当 cmd = setState 时，data 表示一个表达式。
     * - 当 cmd = sendUSB 时，data 支持简写格式（每个字段单个hex表示）：
     *   - 简写模式：
     *       - 写数据：w:[addr1;data1][addr2;data2] - 例如：w:[0xffff;VAR_NAME]
     *       - 读数据：r:[addr1][addr2]
     *   - 任意模式：data 的第一个字节为 cmd，后续 payload 用分号 (;) 分隔。
     */
    var data: String? = null

    var recdata: String? = null

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (!this::event.isInitialized) {
            errors.add("event is required")
        }
        if (!this::cmd.isInitialized) {
            errors.add("cmd is required")
        }
        return errors
    }

    override fun toString(): String {
        return "action(" +
                "event=$event, " +
                "cmd=$cmd, " +
                "data=$data, " +
                "recdata=$recdata" +
                ")"
    }
}

enum class Event {
    changed, clicked, checked, unchecked
}

enum class Cmd {
    sendUSB, setState, resetGUI
}