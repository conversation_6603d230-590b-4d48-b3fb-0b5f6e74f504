<?xml version="1.0" encoding="UTF-8"?>
<uicfg version="2.0.3">
	<vbox>
		<var id="UIO.VERSION"/>
		<var id="UIO.MAINVERSION"/>
		<var id="UIO.SUBVERSION"/>
		<var id="USB.ERROR_CODE"/>		<!-- Required for storing 1st byte of SPI return value (0x11) = uIO-Stick available -->
		<var id="LED.GPIO"/>			<!-- Required for reading hardware status of all pins -->
		<var id="CTRL.M_S_CTRL_7_6"/>	<!-- Required for setting the Mode via buttons -->
		<var id="CTRL.M_S_CTRL"/>		<!-- Required for read back of M_S_CTRL register via SPI and displaying mode -->
		<var id="CTRL.SUP_STAT_1"/>		<!-- Required for display status via LEDs -->
		<var id="CTRL.SUP_STAT_0"/>
		<var id="CTRL.THERM_STAT"/>
		<var id="CTRL.DEV_STAT"/>
		<var id="CTRL.BUS_STAT"/>
		<var id="CTRL.WK_STAT_0"/>
		<var id="CTRL.WK_STAT_1"/>
		<var id="CTRL.WK_LVL_STAT"/>
		<var id="CTRL.GPIO_OC_STAT"/>
		<var id="CTRL.GPIO_OL_STAT"/>
		<var id="UIO.ADC"/>
		<var id="CTRL.FAM_PROD_STAT"/><!-- Required for display of product type and mode -->
		<timer id="INIT" interval="1" oneshot="1" run="1">
			<action event="changed" cmd="sendUSB" data="0x01;0x13;0x55;0xAA"/>			<!-- Reset uIO to default values -->
			<action event="changed" cmd="sendUSB" data="0x02;0x41;0x0a"/>				<!-- SPI Baudrate 1 MHz -->
			<action event="changed" cmd="sendUSB" data="0x02;0x42"/>					<!-- SPI: passive clock level is low, receive on rising edge -->
			<action event="changed" cmd="sendUSB" data="0x02;0x46"/>					<!-- SPI: send MSB first -->
			<action event="changed" cmd="sendUSB" data="0x02;0x48;0x10"/>				<!-- SPI: word length 16 bit -->
			<action event="changed" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/>		<!-- Set SBC Watchdog trigger to 180ms -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;0;20"/>				<!-- Set GPIO Read&Keep for GPIO0 with 20*100ms hold time-->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;1;20"/>				<!-- Set GPIO Read&Keep for GPIO1 with 20*100ms hold time -->
			<action event="changed" cmd="sendUSB" data="0x04;0x36;3;20"/>				<!-- Set GPIO Read&Keep for GPIO3 with 20*100ms hold time -->
			<action event="changed" cmd="sendUSB" data="0x01;0x10" recdata="?;?;UIO.VERSION;UIO.MAINVERSION;UIO.SUBVERSION"/>
		</timer>
		<timer id="TIMER_M_S_CTRL" interval="500" oneshot="0" run="1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x7E" recdata="?;?;CTRL.FAM_PROD_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x01" recdata="?;?;CTRL.M_S_CTRL"/>
			<action event="changed" cmd="sendUSB" data="0x04;0x33" recdata="?;?;2:UIO.ADC"/><!-- read ADC of uIO -->
			<action event="changed" cmd="sendUSB" data="0x04;0x38" recdata="USB.ERROR_CODE;?;LED.GPIO"/><!-- read all GPIO pins -->
		</timer>
		<timer id="TIMER_STATUS_READ" interval="500" oneshot="0" run="1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x40" recdata="?;?;CTRL.SUP_STAT_1"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="?;?;CTRL.SUP_STAT_0"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="?;?;CTRL.DEV_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="?;?;CTRL.BUS_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="?;?;CTRL.WK_STAT_0"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="?;?;CTRL.WK_STAT_1"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x48" recdata="?;?;CTRL.WK_LVL_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x54" recdata="?;?;CTRL.GPIO_OC_STAT"/>
			<action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x55" recdata="?;?;CTRL.GPIO_OL_STAT"/>
		</timer>
		<math visible="false" id="USB.M_S_CTRL" formula="(CTRL.M_S_CTRL_7_6*0x40+CTRL.M_S_CTRL_4_3*0x08+CTRL.M_S_CTRL_2*0x04+CTRL.M_S_CTRL_1_0)">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.M_S_CTRL;0x81"/>
		</math>
		<math visible="false" id="USB.HW_CTRL_0" formula="(CTRL.HW_CTRL_0_6*0x40+CTRL.HW_CTRL_0_5*0x20+CTRL.HW_CTRL_0_2*4+CTRL.HW_CTRL_0_0)">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.HW_CTRL_0;0x82"/>
		</math>
		 <math visible="false" id="USB.WD_CTRL" format="%02x" formula="(CTRL.WD_CTRL_5+CTRL.WD_CTRL_4+1)*1 ? 0x80+CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4 : CTRL.WD_CTRL_5*0x20+CTRL.WD_CTRL_4*0x10+4">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WD_CTRL;0x83"/>
		</math>
		 <math visible="false" id="USB.BUS_CTRL_0" format="%02x" formula="CTRL.BUS_CTRL_0_2_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.BUS_CTRL_0;0x84"/>
		</math>
		 <math visible="false" id="USB.WK_CTRL_0" format="%02x" formula="CTRL.WK_CTRL_0_6*0x40">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_CTRL_0;0x86"/>
		</math>
		 <math visible="false" id="USB.WK_CTRL_1" format="%02x" formula="CTRL.WK_CTRL_1_7*0x80+CTRL.WK_CTRL_1_5*0x20+CTRL.WK_CTRL_1_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_CTRL_1;0x87"/>
		</math>
		 <math visible="false" id="USB.WK_PUPD_CTRL" format="%02x" formula="CTRL.WK_PUPD_CTRL_7_6*0x40+CTRL.WK_PUPD_CTRL_1_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.WK_PUPD_CTRL;0x88"/>
		</math>
		 <math visible="false" id="USB.BUS_CTRL_3" format="%02x" formula="CTRL.BUS_CTRL_3_4*0x10">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.BUS_CTRL_3;0x8B"/>
		</math>
		 <math visible="false" id="USB.TIMER_CTRL" format="%02x" formula="CTRL.TIMER_CTRL_6_4*0x10+CTRL.TIMER_CTRL_3_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.TIMER_CTRL;0x8C"/>
		</math>
		 <math visible="false" id="USB.HW_CTRL_1" format="%02x" formula="CTRL.HW_CTRL_1_7*0x80+CTRL.HW_CTRL_1_5*0x20+CTRL.HW_CTRL_1_4*0x10">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.HW_CTRL_1;0x8E"/>
		</math>
		 <math visible="false" id="USB.HW_CTRL_2" format="%02x" formula="CTRL.HW_CTRL_2_7_5*0x20+CTRL.HW_CTRL_2_4*0x10+CTRL.HW_CTRL_2_3_2*4">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.HW_CTRL_2;0x8F"/>
		</math>
		 <math visible="false" id="USB.GPIO_CTRL" format="%02x" formula="CTRL.GPIO_CTRL_2_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.GPIO_CTRL;0x97"/>
		</math>
		 <math visible="false" id="USB.PWM_CTRL" format="%02x" type="double" formula="CTRL.PWM_DC*2.55+0.1">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.PWM_CTRL;0x98"/>
		</math>
		 <math visible="false" id="USB.PWM_FREQ_CTRL" format="%02x" formula="CTRL.PWM_FREQ_CTRL_1_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.PWM_FREQ_CTRL;0x9C"/>
		</math>
		 <math visible="false" id="USB.HW_CTRL_3" format="%02x" formula="CTRL.HW_CTRL_3_2*4+CTRL.HW_CTRL_3_1_0">
			<action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.HW_CTRL_3;0x9D"/>
		</math>
        <grid columns="2">
            <vbox>
                <group label="Control Function Lite-SBC">
                    <vbox>
                        <hbox>
                            <vbox>
                                <group label="Mode">
                                    <vbox color="#F0F0F0">
                                        <hbox>
                                            <button label="NORMAL" size="55;25">
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_7_6=0"/>
                                            </button>
                                            <button label="SLEEP" size="55;25">
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_7_6=1"/><!-- enter sleep mode -->
                                                <!-- Next instruction prevents device from entering sleep mode again when accessing M_S_CTRL register -->
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_7_6=0"/>
                                                <!-- Following instructions write restart values, which are valid after exit from sleep -->
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_4_3=0"/>
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_2=0"/>
                                                <action event="clicked" cmd="setState" data="CTRL.HW_CTRL_0_5=0"/>
                                                <action event="clicked" cmd="setState" data="CTRL.TIMER_CTRL_3_0=0"/>
                                                <action event="clicked" cmd="setState" data="CTRL.TIMER_CTRL_6_4=0"/>
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_2=0"/>
                                            </button>
                                            <button label="STOP" size="55;25">
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_7_6=2"/>
                                            </button>
                                            <button label="Soft Reset" size="65;25">
                                                <action event="clicked" cmd="setState" data="CTRL.M_S_CTRL_7_6=3"/><!-- triggers a software reset -->
                                                <action event="clicked" cmd="resetGUI"/>
                                            </button>
                                        </hbox>
                                        <!--In order to deteced the SLEEP mode the result of CTRL.FAM_PROD_STAT is used, because in SLEEP mode and NORMAL mode CTRL.M_S_CTRL is 0 -->
                                        <math visible="false" id="LED.MODE" formula="((CTRL.M_S_CTRL*0xC0)==0x00)*CTRL.FAM_PROD_STAT?1:((CTRL.FAM_PROD_STAT==0)?2:((CTRL.M_S_CTRL*0xC0)==0x80?4:8))"/>
                                        <math visible="false" id="LED.PRODUCT" formula="((CTRL.FAM_PROD_STAT==0x54)+(CTRL.FAM_PROD_STAT==0x56))?1:((CTRL.FAM_PROD_STAT==0x55)+(CTRL.FAM_PROD_STAT==0x57))?2:((CTRL.FAM_PROD_STAT==0x5C)+(CTRL.FAM_PROD_STAT==0x5E))?4:((CTRL.FAM_PROD_STAT==0x5D)+(CTRL.FAM_PROD_STAT==0x5F))?8:0"/>
                                        <grid columns="4">
                                            <led data="LED.MODE" bitmask="0x01" oncolor="green" size="17;17"/>
                                            <text label="Normal       "/>
                                            <led data="LED.PRODUCT" bitmask="0x01" oncolor="green" size="17;17"/>
                                            <text label="TLE9461 5.0V"/>
                                            <led data="LED.MODE" bitmask="0x02" oncolor="green" size="17;17"/>
                                            <text label="Sleep / FS"/>
                                            <led data="LED.PRODUCT" bitmask="0x02" oncolor="green" size="17;17"/>
                                            <text label="TLE9461 3.3V"/>
                                            <led data="LED.MODE" bitmask="0x04" oncolor="green" size="17;17"/>
                                            <text label="Stop"/>
                                            <led data="LED.PRODUCT" bitmask="0x04" oncolor="green" size="17;17"/>
                                            <text label="TLE9471 5.0V"/>
                                            <led data="LED.MODE" bitmask="0x08" oncolor="green" size="17;17"/>
                                            <text label="Soft Reset"/>
                                            <led data="LED.PRODUCT" bitmask="0x08" oncolor="green" size="17;17"/>
                                            <text label="TLE9471 3.3V"/>
                                        </grid>
                                    </vbox>
                                </group>
                            </vbox>
                            <vbox>
                                <group label="VCC1">
                                    <vbox color="#F0F0F0">
                                        <checkbox label="OV Reset active" id="CTRL.M_S_CTRL_2"/>
                                        <hbox>
                                            <text label="UV Thresh."/>
                                            <combo id="CTRL.M_S_CTRL_1_0">
                                                <item label="VRT1"/>
                                                <item label="VRT2"/>
                                                <item label="VRT3"/>
                                                <item label="VRT4"/>
                                            </combo>
                                        </hbox>
                                        <checkbox label="UV Release on VRT1" id="CTRL.HW_CTRL_1_7"/>
                                        <hbox>
                                            <text label="Current Lim."/>
                                            <combo id="CTRL.HW_CTRL_3_1_0">
                                                <item label="0.75 A"/>
                                                <item label="1.0 A"/>
                                                <item label="1.2 A"/>
                                                <item label="1.5 A"/>
                                            </combo>
                                        </hbox>
                                        <checkbox label="High act. Peak Thresh." id="CTRL.HW_CTRL_2_4"/>
                                        <text/>
                                    </vbox>
                                </group>
                            </vbox>
                            <vbox>
                                <group label="VCC2 / Charge Pump">
                                    <vbox color="#F0F0F0">
                                        <combo id="CTRL.M_S_CTRL_4_3">
                                            <item label="VCC2 off"/>
                                            <item label="VCC2 on in Normal Mode"/>
                                            <item label="VCC2 on in Nor. + Stop M."/>
                                            <item label="VCC2 always on"/>
                                        </combo>
                                        <checkbox label="Charge Pump" id="CTRL.HW_CTRL_0_2"/>
                                    </vbox>
                                </group>
                                <group label="RSTN Pin Behavior">
                                    <vbox color="#F0F0F0">
                                        <checkbox label="Reduced delay time" id="CTRL.HW_CTRL_1_4"/>
                                        <checkbox label="Triggered by Soft Reset" id="CTRL.HW_CTRL_0_6"/>
                                    </vbox>
                                </group>
                            </vbox>
                        </hbox>
                        <hbox>
                            <vbox>
                                <group label="GPIO, PWM and other pins">
                                    <vbox color="#F0F0F0">
                                        <hbox>
                                            <text label="GPIO"/>
                                            <combo id="CTRL.GPIO_CTRL_2_0" default="0">
                                                <item label="Off" value="4" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                                                <item label="Failure Output" value="0" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                                                <item label="High-Side Timer" value="3" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                                                <item label="Wake Input" value="5"/>
                                                <item label="Low-Side PWM" value="6" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                                                <item label="High-Side PWM" value="7" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                                            </combo>
                                        </hbox>
                                        <hbox>
                                            <text label="Pull Device"/>
                                            <combo id="CTRL.WK_PUPD_CTRL_7_6">
                                                <item label="None"/>
                                                <item label="Pull-down"/>
                                                <item label="Pull-up"/>
                                                <item label="Automatic"/>
                                            </combo>
                                        </hbox>
                                        <hbox>
                                            <text label="PWM Frequency"/>
                                            <combo id="CTRL.PWM_FREQ_CTRL_1_0" >
                                                <item label="100 Hz"/>
                                                <item label="120 Hz"/>
                                                <item label="325 Hz"/>
                                                <item label="400 Hz"/>
                                            </combo>
                                        </hbox>
                                        <hbox>
                                            <text label="PWM Duty C."/>
                                            <doublespinner range="0;100" suffix=" %" id="CTRL.PWM_DC" default="0.0" step="5"/>
                                        </hbox>
                                        <checkbox label="INTN trig. by all status bits" id="CTRL.WK_CTRL_1_7"/>
                                        <checkbox label="CFG1" id="CTRL.HW_CTRL_0_0"/>
                                        <checkbox label="FO_EN" id="CTRL.HW_CTRL_0_5"/>
                                    </vbox>
                                </group>
                                <group label="CAN Configuration">
                                    <vbox color="#F0F0F0">
                                        <combo id="CTRL.BUS_CTRL_0_2_0">
                                            <item label="OFF"/>
                                            <item label="Wake capable"/>
                                            <item label="Receive only"/>
                                            <item label="Normal"/>
                                            <item label="Off (SWK)"/>
                                            <item label="Wake cap. (SWK)"/>
                                            <item label="Rec. only (SWK)"/>
                                            <item label="Normal (SWK)"/>
                                        </combo>
                                        <checkbox label="Slew Rate Control off" id="CTRL.BUS_CTRL_3_4"/>
                                    </vbox>
                                </group>
                            </vbox>
                            <vbox>
                                <group label="Wake-up">
                                    <vbox color="#F0F0F0">
                                        <checkbox label="Enable Wake-up" id="CTRL.WK_CTRL_1_0" default="1"/>
                                        <hbox>
                                            <checkbox label="Voltage Sensing   " id="CTRL.WK_CTRL_1_5" lockon="CTRL.WK_CTRL_1_0=0;CTRL.GPIO_CTRL_2_0=4;CTRL.WK_PUPD_CTRL_1_0=0"/>
                                            <math unit=" V" type="double" formula="((UIO.ADC/1000)*2.33)+0.7"/>
                                        </hbox>
                                        <hbox>
                                            <text label="Pull Device"/>
                                            <combo id="CTRL.WK_PUPD_CTRL_1_0">
                                                <item label="None"/>
                                                <item label="Pull-down"/>
                                                <item label="Pull-up"/>
                                                <item label="Automatic"/>
                                            </combo>
                                        </hbox>
                                        <checkbox label="Enable WK Timer" id="CTRL.WK_CTRL_0_6"/>
                                        <hbox>
                                            <text label="WK Timer Period"/>
                                            <combo id="CTRL.TIMER_CTRL_3_0">
                                                <item label="10 ms"/>
                                                <item label="20 ms"/>
                                                <item label="50 ms"/>
                                                <item label="100 ms"/>
                                                <item label="200 ms"/>
                                                <item label="500 ms"/>
                                                <item label="1 s"/>
                                                <item label="2 s"/>
                                                <item label="5 s"/>
                                                <item label="10 s"/>
                                                <item label="20 s"/>
                                                <item label="50 s"/>
                                                <item label="100 s"/>
                                                <item label="200 s"/>
                                                <item label="500 s"/>
                                                <item label="1000 s"/>
                                            </combo>
                                        </hbox>
                                        <hbox>
                                            <text label="On-time"/>
                                            <combo id="CTRL.TIMER_CTRL_6_4">
                                                <item label="off, HSx is low"/>
                                                <item label="0.1 ms"/>
                                                <item label="0.3 ms"/>
                                                <item label="1.0 ms"/>
                                                <item label="10 ms"/>
                                                <item label="20 ms"/>
                                                <item label="off, HSx is high"/>
                                            </combo>
                                        </hbox>
                                    </vbox>
                                </group>
                                <group label="Thermal Sensing">
                                    <vbox color="#F0F0F0">
                                        <checkbox label="Incr. waiting time for TSD2" id="CTRL.HW_CTRL_1_5"/>
                                        <checkbox label="Incr. threshold for TSD1/2" id="CTRL.HW_CTRL_3_2"/>
                                    </vbox>
                                </group>
                            </vbox>
                            <vbox>
                                <group label="Watchdog">
                                    <vbox color="#F0F0F0">
                                        <checkbox label="Starts after CAN Wake" id="CTRL.WD_CTRL_4"/>
                                        <radio id="CTRL.WD_CTRL_5">
                                            <radiobutton label="Time-out Watchdog"/>
                                            <radiobutton label="Windows Watchdog"/>
                                        </radio>
                                        <togglebutton id="WDT_STOP" label="Stop WDT Trigger">
                                            <action event="unchecked" cmd="sendUSB" data="0x06;0x10;0x01;0x00;0xB4"/><!-- Activate SBC Watchdog trigger -->
                                            <action event="checked" cmd="sendUSB" data="0x06;0x10;0x00;0x00;0xB4"/><!-- Deactivate SBC Watchdog trigger -->
                                        </togglebutton>
                                    </vbox>
                                </group>
                                <group label="Charge Pump">
                                    <vbox color="#F0F0F0">
                                        <text label="Switching Frequency:"/>
                                        <combo id="CTRL.HW_CTRL_2_7_5" default="2">
                                            <item label="1.8 MHz"/>
                                            <item label="2.0 MHz"/>
                                            <item label="2.2 MHz"/>
                                            <item label="2.4 MHz"/>
                                        </combo>
                                        <text label="Spread Spec. Mod. Freq:"/>
                                        <combo id="CTRL.HW_CTRL_2_3_2">
                                            <item label="off"/>
                                            <item label="15.625 kHz"/>
                                            <item label="31.250 kHz"/>
                                            <item label="62.500 kHz"/>
                                        </combo>
                                    </vbox>
                                </group>
                            </vbox>
                        </hbox>
                    </vbox>
                </group>
            </vbox>
            <vbox>
                <group label="Connection Status / Signalisation Pin Status">
                    <grid columns="3">
                        <hbox>
                            <math visible="false" id="LED.USB" formula="(USB.ERROR_CODE==0x11)?1:0"/>
                            <led data="LED.USB" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
                            <text label="uIO Stick connected"/>
                        </hbox>
                        <hbox>
                            <math visible="false" id="LED.TARGET" formula="(CTRL.FAM_PROD_STAT==0)?0:1"/>
                            <led data="LED.TARGET" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
                            <text label="Target IC accessable"/>
                        </hbox>
                        <hbox sizepolicy="fixed;fixed">
                            <text label="uIO Fimware Version:"/>
                            <math formula="UIO.VERSION"/>
                            <text label="."/>
                            <math formula="UIO.MAINVERSION"/>
                            <text label="." />
                            <math formula="UIO.SUBVERSION"/>
                        </hbox>
                        <hbox>
                            <led data="LED.GPIO" bitmask="0x01" oncolor="grey" offcolor="red" size="25;25"/>
                            <text label="RSTN Pin activated"/>
                        </hbox>
                        <hbox>
                            <led data="LED.GPIO" bitmask="0x08" oncolor="grey" offcolor="red" size="25;25"/>
                            <text label="INTN Pin activated"/>
                        </hbox>
                        <hbox>
                            <led data="LED.GPIO" bitmask="0x02" oncolor="red" size="25;25"/>
                            <text label="FO Pin activated"/>
                        </hbox>
                    </grid>
                </group>
                <group label="Status">
                    <vbox>
                        <hbox>
                            <group label="Thermal Status">
                                <grid columns="2">
                                    <led data="CTRL.THERM_STAT" bitmask="0x08" oncolor="green" size="15;15"/>
                                    <text label="TSD2 SAFE"/>
                                    <led data="CTRL.THERM_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
                                    <text label="TSD2"/>
                                    <led data="CTRL.THERM_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
                                    <text label="TSD1"/>
                                    <led data="CTRL.THERM_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="TPW"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="USB.ERROR_CODE;?;CTRL.THERM_STAT"/>
                                    </button>
                                </grid>
                            </group>
                            <group label="Supply Status 1">
                                <grid columns="2">
                                    <led data="CTRL.SUP_STAT_1" bitmask="0x40" oncolor="green" size="15;15"/>
                                    <text label="VS UV"/>
                                    <led data="CTRL.SUP_STAT_1" bitmask="0x20" oncolor="green" size="15;15"/>
                                    <text label="VS OV"/>
                                    <led data="CTRL.SUP_STAT_1" bitmask="0x02" oncolor="green" size="15;15"/>
                                    <text label="VCC1 OV"/>
                                    <led data="CTRL.SUP_STAT_1" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="VCC1 WARN"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC0"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x40" recdata="USB.ERROR_CODE;?;CTRL.SUP_STAT_1"/>
                                    </button>
                                </grid>
                            </group>
                            <group label="Supply Status 0">
                                <grid columns="2">
                                    <led data="CTRL.SUP_STAT_0" bitmask="0x80" oncolor="green" size="15;15"/>
                                    <text label="POR"/>
                                    <led data="CTRL.SUP_STAT_0" bitmask="0x10" oncolor="green" size="15;15"/>
                                    <text label="VCC2 OT"/>
                                    <led data="CTRL.SUP_STAT_0" bitmask="0x08" oncolor="green" size="15;15"/>
                                    <text label="VCC2 UV"/>
                                    <led data="CTRL.SUP_STAT_0" bitmask="0x04" oncolor="green" size="15;15"/>
                                    <text label="VCC1 SC"/>
                                    <led data="CTRL.SUP_STAT_0" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="VCC1 UV"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC1"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="USB.ERROR_CODE;?;CTRL.SUP_STAT_0"/>
                                    </button>
                                </grid>
                            </group>
                            <group label="Device Status">
                                <grid columns="2">
                                    <led data="CTRL.DEV_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
                                    <text label="DEV STAT1"/>
                                    <led data="CTRL.DEV_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                    <text label="DEV STAT0"/>
                                    <led data="CTRL.DEV_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
                                    <text label="SPI FAIL"/>
                                    <led data="CTRL.DEV_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="FAILURE"/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC3"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="USB.ERROR_CODE;?;CTRL.DEV_STAT"/>
                                    </button>
                                    <led data="CTRL.DEV_STAT" bitmask="0x08" oncolor="green" size="15;15"/>
                                    <text label="WD FAIL1"/>
                                    <led data="CTRL.DEV_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
                                    <text label="WD FAIL0"/>
                                </grid>
                            </group>
                        </hbox>
                        <hbox>
                            <group label="Bus Status">
                                <grid columns="2">
                                    <led data="CTRL.BUS_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
                                    <text label="CAN TO"/>
                                    <led data="CTRL.BUS_STAT" bitmask="0x08" oncolor="green" size="15;15"/>
                                    <text label="SYS ERR"/>
                                    <led data="CTRL.BUS_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
                                    <text label="CAN FAIL1"/>
                                    <led data="CTRL.BUS_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
                                    <text label="CAN FAIL2"/>
                                    <led data="CTRL.BUS_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="VCAN UV"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC4"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="USB.ERROR_CODE;?;CTRL.BUS_STAT"/>
                                    </button>
                                </grid>
                            </group>
                            <group label="GPIO OC/OL Status">
                                <grid columns="2">
                                    <led data="CTRL.GPIO_OC_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                    <text label="HS_LS_OC"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xD4"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x54" recdata="USB.ERROR_CODE;?;CTRL.GPIO_OC_STAT"/>
                                    </button>
                                    <led data="CTRL.GPIO_OL_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                    <text label="HS_OL"/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xD5"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x55" recdata="USB.ERROR_CODE;?;CTRL.GPIO_OL_STAT"/>
                                    </button>
                                </grid>
                            </group>
                            <group label="Wake Level Status">
                                <grid columns="2">
                                    <led data="CTRL.WK_LVL_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
                                    <text label="SBC DEV"/>
                                    <led data="CTRL.WK_LVL_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                    <text label="CFG0"/>
                                    <led data="CTRL.WK_LVL_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
                                    <text label="GPIO_LVL"/>
                                    <led data="CTRL.WK_LVL_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="WK_LVL"/>
                                    <text/>
                                    <text/>
                                </grid>
                            </group>
                            <group label="Wake Status 0 + 1">
                                <grid columns="2">
                                    <led data="CTRL.WK_STAT_0" bitmask="0x20" oncolor="green" size="15;15"/>
                                    <text label="CAN"/>
                                    <led data="CTRL.WK_STAT_0" bitmask="0x10" oncolor="green" size="15;15"/>
                                    <text label="TIMER"/>
                                    <led data="CTRL.WK_STAT_0" bitmask="0x01" oncolor="green" size="15;15"/>
                                    <text label="WK"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC6"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="USB.ERROR_CODE;?;CTRL.WK_STAT_0;?"/>
                                    </button>
                                    <led data="CTRL.WK_STAT_1" bitmask="0x10" oncolor="green" size="15;15"/>
                                    <text label="GPIO"/>
                                    <text/>
                                    <button label="CLEAR" size="45;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC7"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="USB.ERROR_CODE;?;CTRL.WK_STAT_1"/>
                                    </button>
                                </grid>
                            </group>
                        </hbox>
                        <hbox>
                            <button label="CLEAR DIAGNOSTIC STATUS">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC0"/><!-- clear SUP_STAT_1-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x40" recdata="?;?;CTRL.SUP_STAT_1"/><!-- read SUP_STAT_1-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC1"/><!-- clear SUP_STAT_0-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="?;?;CTRL.SUP_STAT_0"/><!-- read SUP_STAT_0-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/><!-- clear THERM_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/><!-- read THERM_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC3"/><!-- clear DEV_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x43" recdata="?;?;CTRL.DEV_STAT"/><!-- read DEV_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC4"/><!-- clear BUS_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x44" recdata="?;?;CTRL.BUS_STAT"/><!-- read BUS_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC6"/><!-- clear WK_STAT0-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x46" recdata="?;?;CTRL.WK_STAT_0"/><!-- read WK_STAT0-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC7"/><!-- clear WK_STAT1-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x47" recdata="?;?;CTRL.WK_STAT_1"/><!-- read WK_STAT1-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xD4"/><!-- clear GPIO_OC_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x54" recdata="?;?;CTRL.GPIO_OC_STAT"/><!-- read GPIO_OC_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xD5"/><!-- clear GPIO_OL_STAT-->
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x55" recdata="?;?;CTRL.GPIO_OL_STAT"/><!-- read GPIO_OL_STAT-->
                            </button>
                            <togglebutton id="STATUS_REG_READ_STOP" label="STOP PERIODICAL READ OF STATUS REGISTER">
                                <action event="checked" cmd="setState" data="TIMER_STATUS_READ.run=0"/>
                                <action event="unchecked" cmd="setState" data="TIMER_STATUS_READ.run=1"/>
                            </togglebutton>
                        </hbox>
                    </vbox>
                </group>
            </vbox>
        </grid>
	</vbox>
</uicfg>
