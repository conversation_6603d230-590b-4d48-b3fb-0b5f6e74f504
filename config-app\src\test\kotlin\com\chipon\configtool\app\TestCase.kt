package com.chipon.configtool.app

import javafx.scene.Scene
import javafx.scene.control.ScrollPane
import javafx.stage.Stage
import org.junit.jupiter.api.extension.ExtendWith
import org.testfx.api.FxRobot
import org.testfx.api.FxToolkit
import org.testfx.framework.junit5.ApplicationExtension
import java.io.File
import java.util.concurrent.CountDownLatch

/**
 * Base class for TestFX based UI tests to increase toolkit timeout.
 */
@ExtendWith(ApplicationExtension::class)
abstract class TestCase : FxRobot() {
    companion object {

        val HEXON_TLE94x1: File = File(PROJECT_ROOT, "profiles/HEXON_TLE94x1.xml").absoluteFile

        init {
            FxToolkit.toolkitContext().setupTimeoutInMillis = 30_000
        }
    }

    private val primaryStage: Stage = FxToolkit.registerPrimaryStage()

    fun setUpFixture(runnable: (root: ScrollPane) -> Unit) {
        FxToolkit.setupFixture {
            val root = ScrollPane().apply {
                this.hbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
                this.vbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
            }
            primaryStage.scene = Scene(root)

            runnable.invoke(root)
        }
    }

    /**
     * 带UI显示
     */
    fun setUpFixtureUI(runnable: (root: ScrollPane) -> Unit) {
        FxToolkit.setupFixture {
            val root = ScrollPane().apply {
                this.hbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
                this.vbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
            }
            primaryStage.scene = Scene(root)

            runnable.invoke(root)
            primaryStage.show()
        }
    }

    /**
     * 手动测试观察版本
     */
    fun setUpFixtureShow(runnable: (root: ScrollPane) -> Unit) {
        FxToolkit.setupFixture {
            val root = ScrollPane().apply {
                this.hbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
                this.vbarPolicy = ScrollPane.ScrollBarPolicy.NEVER
            }
            primaryStage.scene = Scene(root)

            runnable.invoke(root)
            primaryStage.show()
        }

        CountDownLatch(1).await()
    }
} 