package com.chipon.configtool.app

import com.chipon.configtool.app.TestCase.Companion.HEXON_TLE94x1

/**
 * Convert sample file to hexon format
 *
 * <AUTHOR>
 */
fun main() {
    val text = HEXON_TLE94x1.readText()
    HEXON_TLE94x1.writeText(
        text
            .replace("setDef", "setState")
            .replace("doublespinbox", "doublespinner")
            .replace("240;240;240", "#F0F0F0")
    )
}
