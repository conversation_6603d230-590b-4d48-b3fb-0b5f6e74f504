package com.chipon.configtool.app.render

import com.chipon.configtool.core.model.LockOn
import com.chipon.configtool.core.model.control.ComboItem
import javafx.beans.property.BooleanProperty
import javafx.scene.Node
import javafx.scene.control.ComboBox
import javafx.scene.control.Spinner
import javafx.scene.control.Toggle
import javafx.scene.layout.VBox
import tornadofx.*

/**
 * <AUTHOR> Wu
 */

@Suppress("unchecked_cast")
fun bindComboLockOn(combo: ComboBox<*>, contexts: Map<String, BaseContext>) {
    val comboBox = combo as ComboBox<ComboItem>

    // Listen to selection changes and apply lock-on behavior
    comboBox.selectionModel.selectedItemProperty().addListener { _, oldValue, selectedItem ->
        //restore previous state
        oldValue.lockon?.lockers?.forEach { locker ->
            val context = contexts[locker.id] ?: return@forEach
            val uiContext = context as? UIContext ?: return@forEach
            val control = uiContext.control
            control.disableProperty().set(false)
        }

        // Apply lock-on behavior for selected item
        selectedItem.lockon?.lockers?.forEach { locker ->
            val context = contexts[locker.id] ?: return@forEach
            val uiContext = context as? UIContext ?: return@forEach
            val control = uiContext.control

            // Disable the target control
            control.disableProperty().set(true)

            // Set the target control value
            setControlValue(control, locker.state)
        }
    }
}

@Suppress("unchecked_cast")
fun bindLockOn(selectedProperty: BooleanProperty, lockOn: LockOn?, contexts: Map<String, BaseContext>) {
    lockOn?.lockers?.forEach { lockedItem ->
        val context = contexts[lockedItem.id] ?: throw ContextNotFoundException(lockedItem.id)
        val uiContext = context as? UIContext ?: throw IllegalStateException("Except lock on a UI control, but got a ${context.holder::class.simpleName}")
        uiContext.control.disableWhen(selectedProperty)

        val control = uiContext.control
        //lock to value
        selectedProperty.addListener { _, _, isSelected ->
            if (isSelected) {
                setControlValue(control, lockedItem.state)
            }
        }
    }
}

@Suppress("unchecked_cast")
private fun setControlValue(control: Node, lockerValue: Int) {
    when (control) {
        is ComboBox<*> -> {
            val comboBox = control as ComboBox<ComboItem>
            val targetItem: ComboItem? = comboBox.items.find { it.value == lockerValue }
            if (targetItem == null) {
                throw IllegalStateException("Bad lock-on value for ComboItem: $lockerValue")
            }
            if (comboBox.selectionModel.selectedItem != targetItem) {
                comboBox.selectionModel.select(targetItem)
            }
        }

        is Spinner<*> -> {
            control as Spinner<Number>
            control.valueFactory.value = lockerValue
        }

        is Toggle -> {
            control.isSelected = lockerValue == 1
        }
    }
    if (control is VBox && control.isRadioGroup()) {
        val toggleGroup = control.toRadioGroup()
        val target: Toggle? = toggleGroup.toggles.find { it.properties["tornadofx.toggleGroupValue"] as Int == lockerValue }
        if (target == null) {
            throw IllegalStateException("Bad lock-on value for radiobutton: $lockerValue")
        }
        toggleGroup.selectToggle(target)
    }
}
