# config_tool

## 环境
* jdk21, 建议下载[JBRSDK](https://github.com/JetBrains/JetBrainsRuntime/releases/tag/jbr-release-21.0.7b1038.54#:~:text=jbrsdk%2D21.0.7%2Dwindows%2Dx64%2Db1038.54.zip) 或者自取 Z:\临时文件\cswu\soft\jbrsdk-21.0.6
* intellij 官网最新版 或 vscode装java套件+kotlin官方插件(很卡不建议)
* 插件需要启用gradle,kotlin

## 使用Jetbrains JDK 支持更多的Hot reload场景
* 下载JBRSDK  
* vmargs: -XX:+AllowEnhancedClassRedefinition

## 统一格式化文件导入
* 设置路径: File | Settings | Editor | Code Style -> Import Scheme
* 文件: [source_formatter.xml](source_formatter.xml)

## 生成html格式协议文档
pandoc protocol.md -o build/通信协议说明.html --standalone --highlight-style=pygments --embed-resources --css=style.css

## This project uses [Gradle](https://gradle.org/).
To build and run the application, use the *Gradle* tool window by clicking the
Gradle icon in the right-hand toolbar,
or run it directly from the terminal:

* Run `./gradlew run` to build and run the application.
* Run `./gradlew build` to only build the application.
* Run `./gradlew check` to run all checks, including tests.
* Run `./gradlew clean` to clean all build outputs.

Note the usage of the Gradle Wrapper (`./gradlew`).
This is the suggested way to use Gradle in production projects.

This project uses a version catalog (see `gradle/libs.versions.toml`) to declare
and version dependencies

## 参考内容
* kotlin 语法:https://kotlinlang.org/docs/home.html
* kotlin 代码看不懂? 菜单栏->Tools-> Kotlin -> Show Kotlin Bytecodes -> 右侧Kotlin窗口点Decompile, 编译后的java语法一目了然
* 主要使用的javafx脚手架: https://edvin.gitbooks.io/tornadofx-guide
* 主题和一些额外控件: atlantaFX [文档](https://mkpaz.github.io/atlantafx/getting-started/) 和 [sampler](https://downloads.hydraulic.dev/atlantafx/sampler/download.html)
* 仪表板控件: https://github.com/HanSolo/medusa
* icon id参考:https://kordamp.org/ikonli/cheat-sheet-materialdesign2.html
