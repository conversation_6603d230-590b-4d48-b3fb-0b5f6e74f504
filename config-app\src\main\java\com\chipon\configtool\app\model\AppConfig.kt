package com.chipon.configtool.app.model

import com.chipon.configtool.app.applyTheme
import com.chipon.configtool.app.views.EmptyView.app
import javafx.beans.property.SimpleDoubleProperty
import javafx.beans.property.SimpleIntegerProperty
import javafx.beans.property.SimpleStringProperty
import tornadofx.*

/**
 * <AUTHOR>
 */
object AppConfig {

    const val THEME_KEY = "theme"

    val theme = stringCfg(THEME_KEY, THEME_KEY).apply {
        addListener { _, _, newValue ->
            runLater {
                applyTheme(newValue)
            }
        }
    }

    val width = doubleCfg("width", 900.0)
    val height = doubleCfg("height", 500.0)

    val serialBaudRate = intCfg("serialBaudRate", 115200)
    val serialDataBit = intCfg("serialDataBit", 8)
    val serialStopBit = doubleCfg("serialStopBit", 1.0)
    val serialParity = stringCfg("serialParity", "无")
    val serialFlowControl = stringCfg("serialFlowControl", "无")

}

/* ------------------------------------------------------------ */
/* Helper creators that keep AppConfig and TornadoFX Config    */
/* synchronised whenever the value changes.                    */
/* ------------------------------------------------------------ */

private fun stringCfg(key: String, default: String = "") =
    SimpleStringProperty(app.config.string(key, default)).apply {
        addListener { _, _, newValue ->
            app.config.set(key to newValue)
            app.config.save()
        }
    }

private fun intCfg(key: String, default: Int) =
    SimpleIntegerProperty(app.config.int(key, default)).apply {
        addListener { _, _, newValue ->
            app.config.set(key to newValue.toInt())
            app.config.save()
        }
    }

private fun doubleCfg(key: String, default: Double) =
    SimpleDoubleProperty(app.config.double(key, default)).apply {
        addListener { _, _, newValue ->
            if (newValue != Double.NaN) {
                app.config.set(key to newValue.toDouble())
                app.config.save()
            }
        }
    }
