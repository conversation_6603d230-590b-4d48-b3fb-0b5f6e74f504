<?xml version="1.0" encoding="UTF-8"?>
<uicfg version="2.0.3">
    <tab label = "Application">
        <!-- 变量定义 -->
        <var id="UIO.VERSION"/>
        <var id="UIO.MAINVERSION"/>
        <var id="UIO.SUBVERSION"/>
        <var id="USB.ERROR_CODE"/>
        <var id="LED.GPIO"/>
        <var id="CTRL.RES"/>
        <var id="CTRL.EN"/>
        <var id="STATUS.ACTIVE"/>
        <var id="STATUS.MEAS_BUSY"/>
        <var id="STATUS.MEAS_AUTO"/>
        <var id="STATUS.DIAGNOSTIC"/>
        <var id="STATUS.DIAGN_ERR"/>
        <var id="STATUS.ERROR"/>
        <var id="STATUS.CRCERR"/>
        <var id="STATUS.OK"/>
        <var id="STATUS.P"/>
        <var id="RAIN1.LED1"/>
        <var id="RAIN1.LED2"/>
        <var id="RAIN1.DC"/>
        <var id="RAIN2.LED1"/>
        <var id="RAIN2.LED2"/>
        <var id="RAIN2.DC"/>
        <var id="LIGHT.LS1"/>
        <var id="LIGHT.LS2"/>
        <var id="LIGHT.LS3"/>
        <var id="LIGHT.LS4"/>
        <var id="LIGHT.LS5"/>
        <var id="ENV.TEMP"/>
        <var id="ENV.VCC"/>
        <var id="ENV.AIN"/>
        <var id="DIAG.DIAG1"/>
        <var id="DIAG.DIAG2"/>
        <var id="DIAG.DIAG3"/>
        <var id="DIAG.DIAG4"/>
        <var id="DIAG.DIAG5"/>
        <var id="DIAG.LSGND_ERR"/>
        <var id="DIAG.RS1_ERR"/>
        <var id="DIAG.LED1_ERR"/>
        <var id="DIAG.LED2_ERR"/>
        <var id="SAT.RS1_LED1"/>
        <var id="SAT.RS1_LED2"/>
        <var id="SAT.RS2_LED1"/>
        <var id="SAT.RS2_LED2"/>
        <var id="SAT.LS1"/>
        <var id="SAT.LS2"/>
        <var id="SAT.LS3"/>
        <var id="SAT.LS4"/>
        <var id="SAT.LS5"/>
        <var id="TEST.TEST1_1"/>
        <var id="TEST.TEST1_2"/>
        <var id="TEST.TEST1_3"/>
        <var id="TEST.TEST1_4"/>
        <var id="TEST.TEST1_5"/>
        <var id="TEST.TEST2_1"/>
        <var id="TEST.TEST2_2"/>
        <var id="TEST.TEST2_3"/>
        <var id="TEST.TEST2_4"/>
        <var id="TEST.TEST2_5"/>
        <var id="ACCUM.LS2"/>
        <var id="ACCUM.LS3"/>
        <var id="RMSO.DATA"/>
        <var id="LOG.EXCEL"/>
        <var id="LOG.DELIMITER"/>
        <var id="LOG.FILENAME"/>
        <var id="LOG.DIRPATH"/>
        <var id="MEAS.REFRESH_TIME"/>
        <var id="MEAS.DIAGNOSTIC"/>
        <var id="MEAS.LOOPS"/>
        <var id="CONNECTION.STATUS"/>
        <var id="CONNECTION.PORT"/>
        <var id="CONNECTION.RX"/>
        <var id="CONNECTION.TX"/>
        <var id="CONNECTION.LOG"/>

        <!-- 初始化定时器 -->
<!--        <timer id="INIT" interval="1" oneshot="1" run="1">-->
<!--            <action event="changed" cmd="sendUSB" data="0x01;0x13;0x55;0xAA"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x41;0x0a"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x42"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x46"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x48;0x10"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x01;0x10" recdata="?;?;UIO.VERSION;UIO.MAINVERSION;UIO.SUBVERSION"/>-->
<!--        </timer>-->

<!--        &lt;!&ndash; 状态读取定时器 &ndash;&gt;-->
<!--        <timer id="TIMER_STATUS_READ" interval="150" oneshot="0" run="1">-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x01" recdata="?;?;CTRL.DR"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x02" recdata="?;?;CTRL.RES"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x03" recdata="?;?;CTRL.EN"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x10" recdata="?;?;RAIN1.LED1"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x11" recdata="?;?;RAIN1.LED2"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x12" recdata="?;?;RAIN1.DC"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x20" recdata="?;?;LIGHT.LS1"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x21" recdata="?;?;LIGHT.LS2"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x22" recdata="?;?;LIGHT.LS3"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x23" recdata="?;?;LIGHT.LS4"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x24" recdata="?;?;LIGHT.LS5"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x30" recdata="?;?;ENV.TEMP"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x31" recdata="?;?;ENV.VCC"/>-->
<!--            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x32" recdata="?;?;ENV.AIN"/>-->
<!--        </timer>-->

        <!-- 主布局 -->
        <vbox>
            <!-- tab布局 -->
            <hbox>
                <!-- 左侧控制面板 -->
                <vbox>
                    <hbox>
                        <!-- 配置控制区域 -->
                        <group label="Configuration">
                            <grid columns="3">
                                <checkbox label="DR" size="60;-1" id="CTRL.DR"/>
                                <checkbox label="RES" size="60;-1" id="CTRL.RES"/>
                                <checkbox label="EN" size="60;-1" id="CTRL.EN" default="1"/>
                                <button label="Read" size="60;21">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x01" recdata="?;?;CTRL.DR"/>
                                </button>
                                <button label="Read" size="60;21">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x02" recdata="?;?;CTRL.RES"/>
                                </button>
                                <button label="Write" size="60;21">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;CTRL.DR;0x01"/>
                                </button>
                            </grid>
                        </group>

                        <!-- RMSO读取数据显示 -->
                        <group label="RMSO Read Data">
                            <vbox>
                                <text label="RMSO read data" size="600;100"/>
                                <button label="...." size="600;15"/>
                            </vbox>
                        </group>
                    </hbox>

                    <hbox>
                        <!-- 雨量通道1 -->
                        <group label="Rain channel 1">
                            <grid columns="3">
                                <text size="20;-1" label="LED1"/>
                                <text size="20;-1" label="LED2"/>
                                <text size="20;-1" label="DC"/>
                                <math size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <text/>
                                <text label="H"/>
                                <text/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <text/>
                                <text label="L"/>
                                <text/>
                            </grid>
                        </group>

                        <!-- 雨量通道2 -->
                        <group label="Rain channel 2">
                            <grid columns="3">
                                <text size="20;-1" label="LED1"/>
                                <text size="20;-1" label="LED2"/>
                                <text size="20;-1" label="DC"/>
                                <math color="#FFFFFF" size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <text/>
                                <text label="H"/>
                                <text/>
                                <progressbar vertical="true" default="0.5" color="#fcba03"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <text/>
                                <text label="L"/>
                                <text/>
                            </grid>
                        </group>

                        <!-- 光通道监控 -->
                        <group label="Light Channels">
                            <grid columns="5">
                                <text label="LS1"/>
                                <text label="LS2"/>
                                <text label="LS3"/>
                                <text label="LS4"/>
                                <text label="LS5"/>
                                <math color="#F0EBEB" formula="0"/>
                                <math color="#F0EBEB" formula="0"/>
                                <math color="#F0EBEB" formula="0"/>
                                <math color="#F0EBEB" formula="0"/>
                                <math color="#F0EBEB" formula="0"/>
                                <text/>
                                <text/>
                                <text label="H"/>
                                <text/>
                                <text/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <text/>
                                <text/>
                                <text label="L"/>
                                <text/>
                                <text/>
                            </grid>
                        </group>
                    </hbox>

                    <hbox>
                        <!-- 环境传感器 -->
                        <group label="Environmental Sensors">
                            <grid columns="3">
                                <text size="20;-1" label="TEMP"/>
                                <text size="20;-1" label="VCC"/>
                                <text size="20;-1" label="AIN"/>
                                <math size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <math size="20;-1" formula="0"/>
                                <text/>
                                <text label="H"/>
                                <text/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <progressbar vertical="true"/>
                                <text/>
                                <text label="L"/>
                                <text/>
                            </grid>
                        </group>

                        <!-- 诊断区域 -->
                        <group label="Diagnostics">
                            <hbox>
                                <grid columns="5">
                                    <text label="DIAG 1"/>
                                    <text label="DIAG 2"/>
                                    <text label="DIAG 3"/>
                                    <text label="DIAG 4"/>
                                    <text label="DIAG 5"/>
                                    <checkbox label="LS1_ERR"/>
                                    <checkbox label="LS2_ERR"/>
                                    <checkbox label="LS3_ERR"/>
                                    <checkbox label="LS4_ERR"/>
                                    <checkbox label="LS5_ERR"/>
                                    <text/>
                                    <text/>
                                    <text label="Test1"/>
                                    <text/>
                                    <text/>
                                    <math color="#F0EBEB" formula="100"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <text/>
                                    <text/>
                                    <text label="Test2"/>
                                    <text/>
                                    <text/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                </grid>
                                <vline/>
                                <grid columns="2">
                                    <text label="Accumulator"/>
                                    <text label="diagnostic"/>
                                    <text label="LS2"/>
                                    <text label="LS3"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                </grid>
                            </hbox>
                        </group>
                    </hbox>
                    <hbox>
                        <!-- 饱和计数器 -->
                        <group label="Saturation counters">
                                <grid columns="9">
                                    <text label="RS1 LED1" size="65;-1"/>
                                    <text label="RS1 LED2" size="65;-1"/>
                                    <text label="RS2 LED1" size="65;-1"/>
                                    <text label="RS2 LED2" size="65;-1"/>
                                    <text label="LS1" size="65;-1"/>
                                    <text label="LS2" size="65;-1"/>
                                    <text label="LS3" size="65;-1"/>
                                    <text label="LS4" size="65;-1"/>
                                    <text label="LS5" size="65;-1"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <math color="#F0EBEB" formula="0"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                    <progressbar vertical="true"/>
                                </grid>
                        </group>
                        <group label="err">
                            <vbox>
                                <checkbox label="LSGND_ERR" id="DIAG.LSGND_ERR"/>
                                <checkbox label="RS1_ERR" id="DIAG.RS1_ERR"/>
                                <checkbox label="LED1_ERR" id="DIAG.LED1_ERR"/>
                                <checkbox label="LED2_ERR" id="DIAG.LED2_ERR"/>
                            </vbox>
                        </group>
                    </hbox>

                    <hbox>
                        <!-- 日志控制 -->
                        <group label="Logging Controls">
                        <hbox>
                            <checkbox label="Excel log" id="LOG.EXCEL"/>
                            <hbox>
                                <text label="Delimiter"/>
                                <combo id="LOG.DELIMITER" default="0">
                                    <item label=";" value="0"/>
                                    <item label="," value="1"/>
                                    <item label="Tab" value="2"/>
                                </combo>
                            </hbox>
                            <hbox>
                                <button label="File Name" size="80;25"/>
                                <button label="Dir path" size="80;25"/>
                            </hbox>
                            <text label="The log path is not set!"/>
                        </hbox>
                    </group>
                    </hbox>

                </vbox>

                <!-- 右侧命令面板 -->
                <vbox>
                    <!-- 命令组 -->
                    <group label="Commands">
                        <vbox>
                            <button label="NOP" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0x01"/>
                            </button>
                            <button label="CRES" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x01;0x01"/>
                            </button>
                            <button label="STBYR" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x02;0x01"/>
                            </button>
                            <button label="WDCLR" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x03;0x01"/>
                            </button>
                            <button label="ACM" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x04;0x01"/>
                            </button>
                            <button label="MMSR" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x05;0x01"/>
                            </button>
                            <button label="AMSR" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x06;0x01"/>
                            </button>
                            <button label="SMSR" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x07;0x01"/>
                            </button>
                            <button label="RMSO CONF" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x08;0x01"/>
                            </button>
                            <button label="RMSO 10W" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x09;0x01"/>
                            </button>
                            <button label="RDIAG" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x0A;0x01"/>
                            </button>
                            <button label="CLRSTAT" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x0B;0x01"/>
                            </button>
                            <button label="CLRDIAG" size="100;40">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x0C;0x01"/>
                            </button>
                        </vbox>
                    </group>


                    <!-- 测量控制 -->
                    <group label="Measurement Controls">
                        <vbox>
                            <vbox>
                                <text label="Refresh time"/>
                                <combo id="MEAS.REFRESH_TIME" default="2">
                                    <item label="50 ms"/>
                                    <item label="100 ms"/>
                                    <item label="150 ms"/>
                                    <item label="200 ms"/>
                                    <item label="500 ms"/>
                                    <item label="1 s"/>
                                </combo>
                                <checkbox label="Diagnostic" id="MEAS.DIAGNOSTIC"/>
                            </vbox>
                            <vbox>
                                <button label="Start measurement" size="120;25">
                                    <action event="clicked" cmd="setState" data="TIMER_STATUS_READ.run=1"/>
                                </button>
                                <button label="Stop measurement" size="120;25">
                                    <action event="clicked" cmd="setState" data="TIMER_STATUS_READ.run=0"/>
                                </button>
                            </vbox>
                            <hbox>
                                <text label="Loops:"/>
                                <text/>
                                <math color="#F0EBEB" formula="0"/>
                                <text/>
                            </hbox>
                        </vbox>
                    </group>
                </vbox>
            </hbox>
            <!-- 公共状态-->
            <hbox>
                <!-- 状态指示器 -->
                <group label="Status Indicators">
                    <hbox>
                        <button label="WD CLR OFF" size="60;20"/>
                        <vline/>
                        <grid columns="7">
                            <checkbox label="ACTIVE" id="STATUS.ACTIVE" default="1"/>
                            <checkbox label="MEAS-BUSY" id="STATUS.MEAS_BUSY"/>
                            <checkbox label="MEAS-AUTO" id="STATUS.MEAS_AUTO"/>
                            <checkbox label="DIAGNOSTIC" id="STATUS.DIAGNOSTIC"/>
                            <checkbox label="DIAGN_ERR" id="STATUS.DIAGN_ERR"/>
                            <checkbox label="ERROR" id="STATUS.ERROR" default="1"/>
                            <checkbox label="CRCERR" id="STATUS.CRCERR"/>
                        </grid>
                        <vline/>
                        <grid columns="2">
                            <checkbox label="OK" id="STATUS.OK" default="1"/>
                            <checkbox label="P" id="STATUS.P"/>
                        </grid>
                    </hbox>
                </group>
            </hbox>
        </vbox>
    </tab>

    <tab label="ConfigRegister">
        <!-- 变量定义 -->
        <var id="CFG.EN_RS1"/>
        <var id="CFG.EN_RS2"/>
        <var id="CFG.EN_LS1"/>
        <var id="CFG.EN_LS2"/>
        <var id="CFG.EN_LS3"/>
        <var id="CFG.EN_LS4"/>
        <var id="CFG.EN_LS5"/>
        <var id="CFG.EN_AIN"/>
        <var id="REG04.ILED1_RNG"/>
        <var id="REG04.ILED1_DAC_CURRENT"/>
        <var id="REG05.ILED2_RNG"/>
        <var id="REG05.ILED2_DAC_CURRENT"/>
        <var id="REG07.LED_SEL"/>
        <var id="REG07.LED_PULSE_WIDTH"/>
        <var id="REG07.NR_LED_PULSES"/>
        <var id="REG08.RS1_GAIN1"/>
        <var id="REG08.RS1_GAIN2"/>
        <var id="REG08.RS2_GAIN1"/>
        <var id="REG08.RS2_GAIN2"/>
        <var id="REG0A.LS1_GAIN"/>
        <var id="REG0A.LS2_GAIN"/>
        <var id="REG0A.LS3_GAIN"/>
        <var id="REG0A.LS4_GAIN"/>
        <var id="REG0A.LS5_GAIN"/>
        <var id="REG0F.VVWD_TIME"/>
        <var id="REG0F.ACC_TH_S2"/>
        <var id="REG0F.ACC_TH_S3"/>
        <var id="REG0F.LED_SET_DIAG"/>
        <var id="REG0F.LED_DIAG"/>
        <var id="REG0F.PD_DIAG"/>
        <var id="REG0F.LSGN_DIAG"/>
        <var id="REG0F.ACC_DIAG"/>
        <var id="REG0F.CH_DIS"/>
        <var id="REG11.BASE_CYCLE"/>
        <var id="REG11.CFG_MSAVG"/>
        <var id="REG11.RS1"/>
        <var id="REG11.RS2"/>
        <var id="REG11.LS1"/>
        <var id="REG11.LS2"/>
        <var id="REG11.LS3"/>
        <var id="REG11.LS4"/>
        <var id="REG11.LS5"/>
        <var id="REG11.VCC"/>
        <var id="REG11.AIN"/>
        <var id="REG11.TS"/>
        <var id="REG11.MS_CNT"/>

        <!-- 主布局 -->
        <vbox>
                <!-- 左侧寄存器配置 -->
                <vbox>
                    <hbox>
                        <!-- 配置使能区域 (红框1) -->
                        <group label="REG03">
                            <vbox>
                                <hbox>
                                    <text label="CFG_MODE:"/>
                                    <combo size="210;-1" id="CFG_MODE">
                                        <item label="model1"/>
                                        <item label="model2"/>
                                        <item label="model3"/>
                                        <item label="model4"/>
                                    </combo>
                                    <button label="Read" size="70;30">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x03;0x00;0x01"/>
                                    </button>
                                </hbox>
                                <grid columns="3">
                                    <checkbox label="EN_RS1" id="CFG.EN_RS1"/>
                                    <checkbox label="EN_RS2" id="CFG.EN_RS2"/>
                                    <button label="Write" size="70;30">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x03;0x00;0x01"/>
                                    </button>
                                    <checkbox label="EN_LS1" id="CFG.EN_LS1"/>
                                    <checkbox label="EN_LS2" id="CFG.EN_LS2"/>
                                    <checkbox label="EN_LS3" id="CFG.EN_LS3"/>
                                    <checkbox label="EN_LS4" id="CFG.EN_LS4"/>
                                    <checkbox label="EN_LS5" id="CFG.EN_LS5"/>
                                    <checkbox label="EN_AIN" id="CFG.EN_AIN"/>
                                </grid>
                            </vbox>
                        </group>

                        <!-- REG04 - ILED1配置 (红框2) -->
                        <group label="REG04">
                            <vbox>
                                <hbox>
                                    <text label="ILED1_RNG[2:0]"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                    <button label="Read"/>
                                </hbox>
                                <hbox>
                                    <text label="ILED1_DAC_CURRENT[7:0]"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                    <button label="Write"/>
                                </hbox>
                            </vbox>
                        </group>

                        <!-- REG05 - ILED1配置 (红框2) -->
                        <group label="REG05">
                            <vbox>
                                <hbox>
                                    <text label="ILED2_RNG[2:0]"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                    <button label="Read"/>
                                </hbox>
                                <hbox>
                                    <text label="ILED2_DAC_CURRENT[7:0]"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                    <button label="Write"/>
                                </hbox>
                            </vbox>
                        </group>






                    </hbox>

                    <hbox>

                        <!-- REG07 - LED配置 (红框4) -->
                        <group label="REG07">
                            <vbox>
                                <hbox>
                                    <text label="LED_SEL[1:0]"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                </hbox>
                                <hbox>
                                    <text size="160;-1" label="LED_PULSE_WIDTH[1:0]"/>
                                    <button label="Read"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                </hbox>
                                <hbox>
                                    <text size="160;-1" label="NR_LED_PULSES[2:0]"/>
                                    <button label="Write"/>
                                </hbox>
                                <hbox>
                                    <combo size="120;-1">
                                        <item label="Range0" value="0"/>
                                    </combo>
                                </hbox>
                            </vbox>
                        </group>

                        <!-- REG08 - RS Gain配置 (红框5) -->
                        <group label="REG08">
                            <grid columns="3">
                                <text label="RS1_GAIN1[2:0]"/>
                                <textinput size="80;-1"/>
                                <button label="Read"/>
                                <text label="RS1_GAIN2[3:0]"/>
                                <textinput size="80;-1"/>
                                <button label="Write"/>
                                <text label="RS2_GAIN1[3:0]"/>
                                <textinput size="80;-1"/>
                                <text/>
                                <text label="RS2_GAIN2[2:0]"/>
                                <textinput size="80;-1"/>
                                <text/>
                            </grid>
                        </group>

                        <!-- REG11 - 基础周期配置 (红框9) -->
                        <group label="REG11">
                            <vbox>
                                <hbox>
                                    <text label="BASE_CYCLE"/>
                                    <combo size="120;-1">
                                        <item label="1.2ms" value="0"/>
                                        <item label="2.4ms" value="1"/>
                                    </combo>
                                    <button label="Read"/>
                                </hbox>
                                <hbox>
                                    <text label="CFG_MSAVG"/>
                                    <combo size="120;-1">
                                        <item label="1cycle" value="0"/>
                                        <item label="2cycle" value="1"/>
                                        <item label="4cycle" value="2"/>
                                        <item label="8cycle" value="3"/>
                                    </combo>
                                    <button label="Write"/>
                                </hbox>
                                <grid columns="5">
                                    <checkbox label="RS1"/>
                                    <checkbox label="RS2"/>
                                    <text/>
                                    <text/>
                                    <text/>
                                    <checkbox label="LS1"/>
                                    <checkbox label="LS2"/>
                                    <checkbox label="LS3"/>
                                    <checkbox label="LS4"/>
                                    <checkbox label="LS5"/>
                                    <checkbox label="VCC"/>
                                    <checkbox label="AIN"/>
                                    <checkbox label="TS"/>
                                    <checkbox label="MS_CNT"/>
                                    <text/>
                                </grid>
                            </vbox>
                        </group>





                    </hbox>

                    <hbox>
                        <!-- REG0A - LS Gain配置 (红框6) -->
                        <group label="REG0A">
                            <grid columns="3">
                                <text label="LS1_GAIN[2:0]"/>
                                <textinput size="80;-1"/>
                                <button label="Read"/>
                                <text label="LS2_GAIN[2:0]"/>
                                <textinput size="80;-1"/>
                                <button label="Write"/>
                                <text label="LS3_GAIN[2:0]"/>
                                <textinput size="80;-1"/>
                                <text/>
                                <text label="LS4_GAIN[2:0]"/>
                                <textinput size="80;-1"/>
                                <text/>
                                <text label="LS5_GAIN[2:0]"/>
                                <textinput size="80;-1"/>
                                <text/>
                            </grid>
                        </group>

                        <!-- REG0F - 阈值和诊断配置 (红框8) -->
                        <group label="REG0F">
                            <vbox>
                                <grid columns="3">
                                    <text label="ACC_THLS2"/>
                                    <combo>
                                        <item label="57696"/>
                                    </combo>
                                    <button label="Read"/>
                                    <text label="ACC_THLS3"/>
                                    <combo>
                                        <item label="57696"/>
                                    </combo>
                                    <button label="Write"/>
                                    <checkbox label="ILED_SET_DIAG"/>
                                    <checkbox label="LED_DIAG"/>
                                    <checkbox label="PD_DIAG"/>
                                    <checkbox label="LSCH_DIAG"/>
                                    <checkbox label="ACC_DIAG"/>
                                    <checkbox label="CH_DIS"/>
                                </grid>
                            </vbox>
                        </group>

                        <vbox>
                            <!-- REG0F - VVWD Time配置 (红框7) -->
                            <group label="REG0F">
                                <vbox>
                                    <hbox>
                                        <text size="120;-1" label="ILED2_RNG[2:0]"/>
                                        <button label="Read"/>
                                    </hbox>
                                    <hbox>
                                        <combo size="120;-1">
                                            <item label="Range0" value="0"/>
                                        </combo>
                                        <button label="Write"/>
                                    </hbox>
                                </vbox>
                            </group>
                            <group label="control">
                                <vbox>
                                    <hbox>
                                        <button label="Read Config" size="200;50">
                                            <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0xFF"/>
                                        </button>
                                    </hbox>
                                    <hbox>
                                        <button label="Write Config" size="200;50">
                                            <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xFF"/>
                                        </button>
                                    </hbox>
                                </vbox>
                            </group>
                        </vbox>

                    </hbox>
                </vbox>
            <!-- 底部状态栏 (红框11) -->
            <hbox>
                <group label="Status">
                    <hbox>
                        <button label="WD CLR OFF" size="80;25"/>
                        <vline/>
                        <grid columns="7">
                            <checkbox label="ACTIVE" id="STATUS.ACTIVE" default="1"/>
                            <checkbox label="MEAS-BUSY" id="STATUS.MEAS_BUSY"/>
                            <checkbox label="MEAS-AUTO" id="STATUS.MEAS_AUTO"/>
                            <checkbox label="DIAGNOSTIC" id="STATUS.DIAGNOSTIC"/>
                            <checkbox label="DIAGN_ERR" id="STATUS.DIAGN_ERR"/>
                            <checkbox label="ERROR" id="STATUS.ERROR" default="1"/>
                            <checkbox label="CRCERR" id="STATUS.CRCERR"/>
                        </grid>
                        <vline/>
                        <grid columns="2">
                            <checkbox label="OK" id="STATUS.OK" default="1"/>
                            <checkbox label="P" id="STATUS.P"/>
                        </grid>
                    </hbox>
                </group>
            </hbox>
        </vbox>
    </tab>

    <tab label="Status registers">
        <!-- 变量定义 -->
        <var id="REG02.POR"/>
        <var id="REG02.SPI_URO"/>
        <var id="REG02.NRES_CLW"/>
        <var id="REG02.NRES_OPW"/>
        <var id="REG02.NRES_CLS"/>
        <var id="REG02.SPI_LIAD"/>
        <var id="REG02.CFG_LED"/>
        <var id="REG02.CFG_RLS"/>
        <var id="REG02.CFG_MC"/>
        <var id="REG02.SPI_ORC"/>
        <var id="REG02.CFG_LED"/>
        <var id="REG02.OTP_DED"/>
        <var id="REG02.CFG_IMS"/>
        <var id="REG02.CFG_IDS"/>
        <var id="REG10.LSGND_ERR"/>
        <var id="REG10.RS2_LSS_ERR"/>
        <var id="REG10.LS1_ERR"/>
        <var id="REG10.RS1_ERR"/>
        <var id="REG10.LS2_ERR"/>
        <var id="REG10.LED2_ERR"/>
        <var id="REG10.LS3_ERR"/>
        <var id="REG10.LED1_ERR"/>
        <var id="REG20.ADC_VAL1_RS1_LED1"/>
        <var id="REG21.ADC_VAL2_RS2_LED1"/>
        <var id="REG22.ADC_VAL3_LS1"/>
        <var id="REG23.ADC_VAL4_LS2"/>
        <var id="REG24.ADC_VAL5_LS3"/>
        <var id="REG25.ADC_VAL6_LS4"/>
        <var id="REG26.ADC_VAL7_LS5"/>
        <var id="REG27.ADC_VAL8_TS"/>
        <var id="REG28.ADC_VAL9_VCC"/>
        <var id="REG29.ADC_VAL10_AIN"/>
        <var id="REG2A.ADC_VAL11_RS1_LED2"/>
        <var id="REG2B.ADC_VAL12_RS2_LED2"/>
        <var id="REG2C.ADC_VAL13_RS1_DC"/>
        <var id="REG2D.ADC_VAL14_RS2_DC"/>
        <var id="REG2E.ADC_VAL15_CNT_R1_H"/>
        <var id="REG2F.ADC_VAL16_CNT_R2_H"/>
        <var id="REG30.ADC_VAL17_CNT_R3_H"/>
        <var id="REG31.ADC_VAL18_CNT_R4_H"/>
        <var id="REG32.ADC_VAL19_DIAG_5"/>

        <!-- 主布局 -->
        <vbox>
            <!-- 寄存器配置区域 -->
            <hbox>
                <!-- 左侧区域 -->
                <vbox>
                    <!-- REG02 配置区域 (红框1) -->
                    <group label="REG02">
                        <grid columns="3">
                            <checkbox label="POR" id="REG02.POR"/>
                            <checkbox label="SPI_URO" id="REG02.SPI_URO"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x02" recdata="?;?;REG02"/>
                            </button>
                            <checkbox label="NRES_CLW" id="REG02.NRES_CLW"/>
                            <checkbox label="SPI_ORC" id="REG02.SPI_ORC"/>
                            <text/>
                            <checkbox label="NRES_OPW" id="REG02.NRES_OPW"/>
                            <checkbox label="CFG_LED" id="REG02.CFG_LED"/>
                            <text/>
                            <checkbox label="NRES_CLS" id="REG02.NRES_CLS"/>
                            <checkbox label="OTP_DED" id="REG02.OTP_DED"/>
                            <text/>
                            <checkbox label="SPI_LIAD" id="REG02.SPI_LIAD"/>
                            <checkbox label="CFG_MC" id="REG02.CFG_MC"/>
                            <text/>
                            <checkbox label="CFG_RLS" id="REG02.CFG_RLS"/>
                            <checkbox label="CFG_IDS" id="REG02.CFG_IDS"/>
                            <text/>
                        </grid>
                    </group>

                    <!-- REG10 错误状态区域 (红框2) -->
                    <group label="REG10">
                        <grid columns="3">
                            <checkbox label="LSGND_ERR" id="REG10.LSGND_ERR"/>
                            <checkbox label="RS2_LSS_ERR" id="REG10.RS2_LSS_ERR"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x10" recdata="?;?;REG10"/>
                            </button>
                            <checkbox label="LS1_ERR" id="REG10.LS1_ERR"/>
                            <checkbox label="RS1_ERR" id="REG10.RS1_ERR"/>
                            <text/>
                            <checkbox label="LS2_ERR" id="REG10.LS2_ERR"/>
                            <checkbox label="LED2_ERR" id="REG10.LED2_ERR"/>
                            <text/>
                            <checkbox label="LS3_ERR" id="REG10.LS3_ERR"/>
                            <checkbox label="LED1_ERR" id="REG10.LED1_ERR"/>
                            <text/>
                        </grid>
                    </group>

                    <!-- REG20-REG24 ADC值区域 (红框3) -->
                    <group label="REG20-REG24">
                        <grid columns="3">
                            <text label="ADC_VAL1_RS1_LED1"/>
                            <textinput size="80;-1" id="REG20.ADC_VAL1_RS1_LED1"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x20" recdata="?;?;REG20"/>
                            </button>
                            <text label="ADC_VAL2_RS2_LED1"/>
                            <textinput size="80;-1" id="REG21.ADC_VAL2_RS2_LED1"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x21" recdata="?;?;REG21"/>
                            </button>
                            <text label="ADC_VAL3_LS1"/>
                            <textinput size="80;-1" id="REG22.ADC_VAL3_LS1"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x22" recdata="?;?;REG22"/>
                            </button>
                            <text label="ADC_VAL4_LS2"/>
                            <textinput size="80;-1" id="REG23.ADC_VAL4_LS2"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x23" recdata="?;?;REG23"/>
                            </button>
                            <text label="ADC_VAL5_LS3"/>
                            <textinput size="80;-1" id="REG24.ADC_VAL5_LS3"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x24" recdata="?;?;REG24"/>
                            </button>
                        </grid>
                    </group>
                </vbox>

                <!-- 中间区域 -->
                <vbox>
                    <!-- REG25-REG29 ADC值区域 (红框4) -->
                    <group label="REG25-REG29">
                        <grid columns="3">
                            <text label="ADC_VAL6_LS4"/>
                            <textinput size="80;-1" id="REG25.ADC_VAL6_LS4"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x25" recdata="?;?;REG25"/>
                            </button>
                            <text label="ADC_VAL7_LS5"/>
                            <textinput size="80;-1" id="REG26.ADC_VAL7_LS5"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x26" recdata="?;?;REG26"/>
                            </button>
                            <text label="ADC_VAL8_TS"/>
                            <textinput size="80;-1" id="REG27.ADC_VAL8_TS"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x27" recdata="?;?;REG27"/>
                            </button>
                            <text label="ADC_VAL9_VCC"/>
                            <textinput size="80;-1" id="REG28.ADC_VAL9_VCC"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x28" recdata="?;?;REG28"/>
                            </button>
                            <text label="ADC_VAL10_AIN"/>
                            <textinput size="80;-1" id="REG29.ADC_VAL10_AIN"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x29" recdata="?;?;REG29"/>
                            </button>
                        </grid>
                    </group>

                    <!-- REG2A-REG2D 继续ADC值区域 -->
                    <group label="REG2A-REG2D">
                        <grid columns="3">
                            <text label="ADC_VAL11_RS1_LED2"/>
                            <textinput size="80;-1" id="REG2A.ADC_VAL11_RS1_LED2"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2A" recdata="?;?;REG2A"/>
                            </button>
                            <text label="ADC_VAL12_RS2_LED2"/>
                            <textinput size="80;-1" id="REG2B.ADC_VAL12_RS2_LED2"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2B" recdata="?;?;REG2B"/>
                            </button>
                            <text label="ADC_VAL13_RS1_DC"/>
                            <textinput size="80;-1" id="REG2C.ADC_VAL13_RS1_DC"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2C" recdata="?;?;REG2C"/>
                            </button>
                            <text label="ADC_VAL14_RS2_DC"/>
                            <textinput size="80;-1" id="REG2D.ADC_VAL14_RS2_DC"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2D" recdata="?;?;REG2D"/>
                            </button>
                        </grid>
                    </group>
                </vbox>

                <!-- 右侧区域 -->
                <vbox>
                    <!-- REG2E-REG32 计数器和诊断区域 (红框5) -->
                    <group label="REG2E-REG32">
                        <grid columns="3">
                            <text label="ADC_VAL15_CNT_R1_H"/>
                            <textinput size="80;-1" id="REG2E.ADC_VAL15_CNT_R1_H"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2E" recdata="?;?;REG2E"/>
                            </button>
                            <text label="ADC_VAL15_CNT_R1_L"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL15_DIAG_1"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL16_CNT_R2_H"/>
                            <textinput size="80;-1" id="REG2F.ADC_VAL16_CNT_R2_H"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x2F" recdata="?;?;REG2F"/>
                            </button>
                            <text label="ADC_VAL16_CNT_R2_L"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL16_DIAG_2"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL17_CNT_R3_H"/>
                            <textinput size="80;-1" id="REG30.ADC_VAL17_CNT_R3_H"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x30" recdata="?;?;REG30"/>
                            </button>
                            <text label="ADC_VAL17_CNT_R3_L"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL17_DIAG_3"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL18_CNT_R4_H"/>
                            <textinput size="80;-1" id="REG31.ADC_VAL18_CNT_R4_H"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x31" recdata="?;?;REG31"/>
                            </button>
                            <text label="ADC_VAL18_CNT_R4_L"/>
                            <textinput size="80;-1"/>
                            <text/>
                            <text label="ADC_VAL18_DIAG_4"/>
                            <textinput size="80;-1"/>
                            <text/>
                        </grid>
                    </group>

                    <!-- REG32 区域 (红框6) -->
                    <group label="REG32">
                        <grid columns="3">
                            <text label="ADC_VAL19_DIAG_5"/>
                            <textinput size="80;-1" id="REG32.ADC_VAL19_DIAG_5"/>
                            <button label="Read" size="70;30">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x32" recdata="?;?;REG32"/>
                            </button>
                        </grid>
                    </group>

                    <!-- Read Status 按钮区域 (红框7) -->
                    <vbox>
                        <button label="Read Status" size="200;50">
                            <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0xFF"/>
                        </button>
                    </vbox>
                </vbox>
            </hbox>

            <!-- 底部状态栏 -->
            <hbox>
                <group label="Status">
                    <hbox>
                        <button label="WD CLR OFF" size="80;25"/>
                        <vline/>
                        <grid columns="7">
                            <checkbox label="ACTIVE" id="STATUS.ACTIVE" default="1"/>
                            <checkbox label="MEAS-BUSY" id="STATUS.MEAS_BUSY"/>
                            <checkbox label="MEAS-AUTO" id="STATUS.MEAS_AUTO"/>
                            <checkbox label="DIAGNOSTIC" id="STATUS.DIAGNOSTIC"/>
                            <checkbox label="DIAGN_ERR" id="STATUS.DIAGN_ERR"/>
                            <checkbox label="ERROR" id="STATUS.ERROR" default="1"/>
                            <checkbox label="CRCERR" id="STATUS.CRCERR"/>
                        </grid>
                        <vline/>
                        <grid columns="2">
                            <checkbox label="OK" id="STATUS.OK" default="1"/>
                            <checkbox label="P" id="STATUS.P"/>
                        </grid>
                    </hbox>
                </group>
            </hbox>
        </vbox>
    </tab>

</uicfg>