package com.chipon.configtool.app

import com.chipon.configtool.app.render.UIRenderer
import com.chipon.configtool.core.model.control.ComboItem
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.scene.control.Button
import javafx.scene.control.ComboBox
import javafx.scene.layout.VBox
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.*
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.testfx.api.FxRobot


/**
 * <AUTHOR> Wu
 */
class ActionTests : TestCase() {

    @Test
    fun `button click update var`(robot: FxRobot) {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <vbox>
                    <var id="var1"/>
                    <var id="var2"/>
                    <var id="var3"/>
                    <button label="test">
                        <action event="clicked" cmd="setState" data="var1=1"/>
                        <action event="clicked" cmd="setState" data="{var2=2,var3=3}"/>
                    </button>
                </vbox>
            </uicfg>
        """.trimIndent()
        )

        lateinit var button: Button
        lateinit var env: UIRenderer.RenderEnv
        setUpFixtureUI { root ->
            // Build UI from profile and obtain rendering env
            env = UIRenderer.buildView(profile, root)

            val content = root.content as VBox
            button = content.children.filterIsInstance<Button>().first()
        }

        // initial value should be 0
        env.defineContexts["var1"]!!.value.toInt() shouldBe 0
        env.defineContexts["var2"]!!.value.toInt() shouldBe 0

        robot.clickOn(button)

        env.defineContexts["var2"]!!.value.toInt() shouldBe 2
        env.defineContexts["var3"]!!.value.toInt() shouldBe 3
    }

    @Test
    fun `button click update combo`(robot: FxRobot) {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <vbox>
                    <combo id="COMBO1">
                        <item label="A" value="0"/>
                        <item label="B" value="1"/>
                    </combo>
                    <button label="Set B">
                        <action event="clicked" cmd="setState" data="COMBO1=1"/>
                    </button>
                </vbox>
            </uicfg>
        """.trimIndent()
        )

        lateinit var combo: ComboBox<*>
        lateinit var button: Button
        lateinit var env: UIRenderer.RenderEnv

        setUpFixtureUI { root ->
            env = UIRenderer.buildView(profile, root)

            val content = root.content as VBox
            combo = content.children[0] as ComboBox<*>
            button = content.children[1] as Button
        }

        // Initially selected item value should be 0 (A)
        env.defineContexts["COMBO1"]!!.value.toInt() shouldBe 0

        // Click button to set combo to value 1 (B)
        robot.clickOn(button)

        // Combo selection and context value should now be 1
        env.defineContexts["COMBO1"]!!.value.toInt() shouldBe 1
        val selectedItem = combo.selectionModel.selectedItem as ComboItem
        selectedItem.value shouldBe 1
    }

    @Test
    fun `button setState updates var and triggers math action`(robot: FxRobot) {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <var id="V1"/>
                    <math visible="false" id="M1" formula="V1*2">
                        <action event="changed" cmd="sendUSB" data="0x00;0x00"/>
                    </math>
                    <button label="SetVar">
                        <action event="clicked" cmd="setState" data="V1=5"/>
                    </button>
                </vbox>
            </uicfg>
        """.trimIndent()

        val profile = XmlConfigParser.parseFromString(xml)

        // Create a mock client
        client = mock<TestClient>()
        doNothing().whenever(client!!).write(any())

        lateinit var env: UIRenderer.RenderEnv
        lateinit var button: Button
        setUpFixtureUI { root ->
            env = UIRenderer.buildView(profile, root)
            button = (root.content as VBox).children[0] as Button
        }

        val varCtx = env.defineContexts["V1"]!!
        val mathCtx = env.defineContexts["M1"]!!

        // initial values
        varCtx.value.toInt() shouldBe 0
        mathCtx.value.toInt() shouldBe 0


        // click button
        robot.clickOn(button)

        // Assertions
        varCtx.value.toInt() shouldBe 5
        mathCtx.value.toInt() shouldBe 10

        verify(client!!, times(1)).write(any())
    }
}