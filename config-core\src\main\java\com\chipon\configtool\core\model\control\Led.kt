package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IValidatable

/**
 * <led>, 默认包含label组件
 */
class Led : BaseUIControl(), IValidatable {

    lateinit var data: String

    /**
     * led的状态从data中的bitmask获取, true=on, false=off
     */
    lateinit var bitmask: String

    var oncolor: LedColor = LedColor.green

    var offcolor: LedColor = LedColor.grey

    fun getBitmaskValue(): Int {
        return when {
            bitmask.startsWith("0x", true) -> bitmask.removePrefix("0x").toInt(16)
            else -> bitmask.toInt()
        }
    }

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()
        if (!this::data.isInitialized || data.isBlank()) {
            errors.add("led data is required")
        }
        if (!this::bitmask.isInitialized || bitmask.isBlank()) {
            errors.add("led bitmask is required")
        } else if (bitmask.startsWith("0x")) {
            val maskVal = Integer.parseInt(this.bitmask.removePrefix("0x"), 16)
            if (maskVal < 0 || maskVal > 0xFFFF) {
                errors.add("led bitmask is out of range")
            }
        }
        return errors
    }

    override fun toString(): String {
        return "led(" +
                "data=$data, " +
                "bitmask=$bitmask, " +
                "oncolor=$oncolor, " +
                "offcolor=$offcolor" +
                ")"
    }
}

enum class LedColor {
    green, red, grey
}
