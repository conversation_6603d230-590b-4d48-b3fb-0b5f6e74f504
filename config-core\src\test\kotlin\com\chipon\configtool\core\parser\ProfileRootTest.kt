package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.ProfileRoot
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.element.Timer
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

/**
 * <AUTHOR> Wu
 */
class ProfileRootTest {

    @Test
    fun `should parse profile root with various children`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Main Tab">
                    <timer id="t1"/>
                    <timer id="t2"/>
                    <group label="Grp"/>
                </tab>
            </uicfg>
        """.trimIndent()

        val root: ProfileRoot = XmlConfigParser.parseFromString(xml)

        root.children.size shouldBe 1

        val tab = root.children[0] as TabContainer
        tab.label shouldBe "Main Tab"
        tab.children.size shouldBe 3

        (tab.children[0] is Timer) shouldBe true
        (tab.children[1] is Timer) shouldBe true
        (tab.children[2] is GroupContainer) shouldBe true
    }

    @Test
    fun `should throw when non container element in root`() {
        val xml = """
            <uicfg version="1.0.0">
                <led label="Power" data="FLAG" bitmask="0x01"/>
            </uicfg>
        """.trimIndent()

        shouldThrow<IllegalStateException> {
            XmlConfigParser.parseFromString(xml)
        }
    }
}