package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.*
import com.chipon.configtool.core.model.element.Cmd
import com.chipon.configtool.core.model.element.Event
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.shouldBe
import java.io.File
import kotlin.test.Test

class XmlConfigParserTest {

    val configFile = File(this::class.java.classLoader.getResource("example-config.xml")!!.toURI())

    @Test
    fun `should parse example configuration file with nested children`() {
        val config = XmlConfigParser.parseFromFile(configFile, false)

        // Verify root structure
        config.version shouldBe "1.0.0"
        config.svd?.file shouldBe "example.svd"
        val children = config.children
        children.size shouldBe 1

        // Verify main tab and its nested structure
        val mainTab = children.find { it is TabContainer } as TabContainer
        mainTab.label shouldBe "Motion Control Configuration"
        mainTab.children.size shouldBe 9 // 6 vars + 2 timers + 1 grid

        // Verify grid layout with nested containers
        val grid = mainTab.children[8] as GridContainer
        grid.columns shouldBe 2

        // Verify left column nested structure
        val leftVbox = grid.children[0] as VerticalContainer
        val connectionGroup = leftVbox.children[0] as GroupContainer
        connectionGroup.label shouldBe "Connection Status"

        // Verify nested grid with controls
        val connectionGrid = connectionGroup.children[0] as GridContainer
        connectionGrid.columns shouldBe 3

        // Verify polymorphic control elements
        val usbLed = connectionGrid.children[0] as Led
        usbLed.data shouldBe "LED.USB"
        usbLed.bitmask shouldBe "0x01"
        usbLed.oncolor shouldBe LedColor.red

        val firmwareText = connectionGrid.children[2] as UIText
        firmwareText.label shouldBe "Firmware Version: 2.2.1"

        // Verify nested control function structure
        val controlGroup = leftVbox.children[1] as GroupContainer
        val controlVbox = controlGroup.children[0] as VerticalContainer
        val modeGroup = controlVbox.children[0] as GroupContainer
        val modeHbox = modeGroup.children[0] as HorizontalContainer

        // Verify buttons with actions in nested structure
        val normalButton = modeHbox.children[0] as UIButton
        normalButton.label shouldBe "NORMAL"
        normalButton.size.width shouldBe 55
        normalButton.actions.size shouldBe 1
        normalButton.actions[0].event shouldBe Event.clicked
        normalButton.actions[0].cmd shouldBe Cmd.sendUSB

        // Verify nested combo with items
        val vccGroup = controlVbox.children[1] as GroupContainer
        val vccVbox = vccGroup.children[0] as VerticalContainer
        val vrtHbox = vccVbox.children[1] as HorizontalContainer
        val vrtCombo = vrtHbox.children[1] as UICombo

        vrtCombo.items.size shouldBe 4
        vrtCombo.items[0].label shouldBe "VRT1"
        vrtCombo.items[0].value shouldBe 0

        // Verify right column nested structure
        val rightVbox = grid.children[1] as VerticalContainer
        val statusGroup = rightVbox.children[0] as GroupContainer
        val statusVbox = statusGroup.children[0] as VerticalContainer

        // Verify nested status groups with grids
        val thermalGroup = statusVbox.children[0] as GroupContainer
        val thermalGrid = thermalGroup.children[0] as GridContainer
        thermalGrid.columns shouldBe 2

        // Verify LED and text pairs in nested grid
        val tsd2Led = thermalGrid.children[0] as Led
        tsd2Led.data shouldBe "CTRL.THERM_STAT"
        tsd2Led.bitmask shouldBe "0x04"
        tsd2Led.size.width shouldBe 15

        val tsd2Text = thermalGrid.children[1] as UIText
        tsd2Text.label shouldBe "TSD2"

        // Verify configuration parameters with spinners and radio
        val configGroup = rightVbox.children[1] as GroupContainer
        val configVbox = configGroup.children[0] as VerticalContainer
        val pwmHbox = configVbox.children[0] as HorizontalContainer
        val pwmSpinner = pwmHbox.children[1] as UIDoubleSpinner
        pwmSpinner.range shouldBe Pair(1.0, 100.0)
        pwmSpinner.suffix shouldBe "kHz"

        val radio = configVbox.children[3] as UIRadio
        radio.radioButtons.size shouldBe 3
        radio.radioButtons[0].label shouldBe "Standard Mode"

        val errors = config.validate()
        errors.shouldBeEmpty()
    }

}