package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IColorable
import com.chipon.configtool.core.model.IDefaultable
import com.chipon.configtool.core.model.IValidatable

/**
 * <progressbar>
 *
 * <AUTHOR>
 */
class UIProgressBar : BaseUIControl(), IDefaultable<Double>, IColorable, IValidatable {

    var vertical: Boolean = false

    override var default: Double = 0.0

    override var color: String? = null

    override fun validate(): List<String> {
        val errors = mutableListOf<String>()

        if (default < 0.0 || default > 1.0) {
            errors.add("Default value must be between 0 and 1.")
        }
        return errors
    }
}