package com.chipon.configtool.net;

import com.chipon.configtool.core.LoggerKt;
import com.chipon.configtool.net.handler.PacketCodec;
import com.chipon.configtool.net.handler.TimeoutHandler;
import com.chipon.configtool.net.packet.IPayload;
import com.chipon.configtool.net.packet.Packet;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import java.util.Objects;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR> Wu
 */
public abstract class BaseClient implements IClient {

    protected ChannelHandler defaultHandlers() {
        return new ChannelInitializer<>() {
            @Override
            protected void initChannel(Channel ch) {
                ch.pipeline().addFirst(new LoggingHandler(LogLevel.DEBUG));
//                ch.pipeline().addFirst(new IdleStateHandler(1, 0, 0, TimeUnit.MILLISECONDS));
                ch.pipeline().addLast(new PacketCodec());
                ch.pipeline().addLast(new TimeoutHandler());
            }
        };
    }

    private short seq;

    @Nullable
    public abstract Channel getChannel();

    @Override
    public boolean isConnected() {
        Channel channel = getChannel();
        return channel != null && channel.isOpen();
    }

    @Override
    public void write(IPayload payload) {
        if (!isConnected()) {
            throw new IllegalStateException("Not connected");
        }
        Objects.requireNonNull(getChannel()).writeAndFlush(new Packet(seq++, payload));
    }

    @Override
    public void close() {
        if (isConnected() && getChannel() != null) {
            try {
                getChannel().close().sync();
            } catch (InterruptedException e) {
                LoggerKt.getLogger().error("Error close channel", e);
            }
        }
    }

}
