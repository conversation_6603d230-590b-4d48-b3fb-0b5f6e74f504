package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.VerticalContainer
import com.chipon.configtool.core.model.control.UITextInput
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class TextInputTest {

    @Test
    fun `textinput regex parsing`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Fields">
                        <vbox>
                            <textinput regex="^0x[0-9A-Fa-f]{8}$" />
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val textInput = (((root.children[0] as <PERSON>b<PERSON><PERSON><PERSON>).children[0] as <PERSON><PERSON>ontaine<PERSON>)
            .children[0] as Vert<PERSON><PERSON>ontaine<PERSON>).children[0] as UITextInput

        textInput.regex.matches("0xDEADBEEF") shouldBe true
        textInput.regex.matches("1234") shouldBe false
    }
} 