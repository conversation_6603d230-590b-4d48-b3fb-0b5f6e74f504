package com.chipon.configtool.app.render

import com.chipon.configtool.app.BadDataFormat
import com.chipon.configtool.app.client
import com.chipon.configtool.app.toIntHex
import com.chipon.configtool.core.logger
import com.chipon.configtool.core.model.IAction
import com.chipon.configtool.core.model.element.*
import com.chipon.configtool.net.packet.*
import javafx.scene.control.Button
import javafx.scene.control.CheckBox
import javafx.scene.control.Toggle
import tornadofx.*

/**
 * <AUTHOR> Wu
 */

// Extension to get byte width
val DataType.size: Int
    get() = when (this) {
        DataType.int8, DataType.uint8 -> 1
        DataType.int16, DataType.uint16 -> 2
        DataType.int32, DataType.uint32 -> 4
        DataType.int64, DataType.uint64, DataType.double -> 8
    }

fun IAction.registerActions(result: Any?, contexts: Map<String, BaseContext>) {
    when (result) {
        //button
        is Button -> {
            val matchedActions = actions.filter { it.event == Event.clicked }
            result.action {
                matchedActions.forEach {
                    it.execute(contexts)
                }
            }
        }
        // CheckBox / Switch / ToggleButton register for checked & unchecked events
        is CheckBox, is Toggle -> {
            val selectProperty = if (result is CheckBox) result.selectedProperty() else (result as Toggle).selectedProperty()
            selectProperty.addListener { _, _, selected ->
                if (selected) {
                    actions.filter { it.event == Event.checked }.map {
                        it.execute(contexts)
                    }
                } else {
                    actions.filter { it.event == Event.unchecked }.map {
                        it.execute(contexts)
                    }
                }
            }
        }

        else -> {
            if (this is MathDefinition) {
                if (visible) {
                    if (!actions.isEmpty()) throw IllegalStateException("visible <math> not allowed actions") else return
                }
                val mathCtx = contexts[this.id] as MathContext
                mathCtx.valueProperty.addListener { _, _, _ ->
                    actions.filter { it.event == Event.changed }.forEach {
                        it.execute(contexts)
                    }
                }
            } else if (this is Timer) {
                val timerCtx = contexts[this.id] as TimerContext
                // TODO: Implement timer related actions when timer fires
            } else {
                throw NotImplementedError()
            }
        }
    }
}

private fun String.parseSimplePayload(contexts: Map<String, BaseContext>): IPayload? {
    val trimmed = trim()
    return when {
        trimmed.startsWith("w:", true) -> {
            val segments = "\\[([^]]+)]".toRegex().findAll(trimmed).map { it.groupValues[1] }.toList()
            if (segments.isEmpty()) throw BadDataFormat("写地址列表为空")
            val list = segments.map { seg ->
                val (addrHex, varId) = seg.split(":", limit = 2)
                val ctx = contexts[varId]!!
                val addr = addrHex.toIntHex()
                VarList(addr, ctx.dataType().size.toShort(), ctx.valueProperty.value.toLong())
            }
            WritePayload(list)
        }
        trimmed.startsWith("r:", true) -> {
            val segments = "\\[([^]]+)]".toRegex().findAll(trimmed).map { it.groupValues[1] }.toList()
            if (segments.isEmpty()) throw BadDataFormat("读地址列表为空")
            val addresses = segments.map { it.toIntHex() }
            ReadPayload(addresses)
        }
        else -> null
    }
}


fun String.bytesArray(contexts: Map<String, BaseContext>): ByteArray {
    try {
        return split(";")
            .map {
                val part = it.trim()
                // If part matches a variable id in contexts, replace with its value
                val value: Number? = contexts[part]?.valueProperty?.value
                val str = value?.toString() ?: part
                str.removePrefix("0x").toInt(16).toByte()
            }
            .toByteArray()
    } catch (e: Exception) {
        logger.error("Bad hex data format: $this", e)
        throw e
    }
}

fun Action.execute(contexts: Map<String, BaseContext>) {
    when (cmd) {
        Cmd.sendUSB -> {
            if (client == null) {
                logger.error("未连接下位机")
                return
            } else if (data == null) {
                logger.error("data is null")
                return
            }

            val simplePayload = data!!.parseSimplePayload(contexts)
            if (simplePayload != null) {
                client!!.write(simplePayload)
            } else {
                client!!.write(parsePayload(data!!.bytesArray(contexts)))
            }
        }

        Cmd.setState -> {
            Evaluator.evaluate(data!!, contexts)
        }

        Cmd.resetGUI -> {
            // TODO: Implement GUI reset
            logger.debug("[Action] resetGUI")
        }
    }
}
