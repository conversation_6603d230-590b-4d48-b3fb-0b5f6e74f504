package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.ProfileRoot
import com.fasterxml.jackson.dataformat.xml.JacksonXmlModule
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import java.io.File

/**
 * Simple XML configuration parser using Jackson XML
 */
object XmlConfigParser {

    val mapper: XmlMapper by lazy {
        val streamFactory = XmlMapper(JacksonXmlModule().apply {
            setDefaultUseWrapper(false)
        })
        XmlMapper.Builder(streamFactory)
            .addModule(KotlinModule.Builder().build())
            .addModule(ConfigElementModule())
//            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .build()
    }

    fun parseFromFile(file: File, reportError: Boolean = true): ProfileRoot {
        return mapper.readValue(file, ProfileRoot::class.java).also {
            val errors = it.validate()
            if (reportError) throwValidateErrors(errors)
        }
    }

    fun parseFromString(xml: String, reportError: Boolean = true): ProfileRoot {
        return mapper.readValue(xml, ProfileRoot::class.java).also {
            val errors = it.validate()
            if (reportError) throwValidateErrors(errors)
        }
    }

    private fun throwValidateErrors(errors: List<String>) {
        if (errors.isNotEmpty()) {
            throw IllegalArgumentException("Validation failed: ${errors.joinToString("\n")}")
        }
    }
} 