[versions]
kotlin = "2.2.0"
kotlinxCoroutines = "1.9.0"

[libraries]
kotlinGradlePlugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
kotlinxCoroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutines" }

annotations = "org.jetbrains:annotations:26.0.2"
guava = "com.google.guava:guava:33.4.8-jre"
netty = "io.netty:netty-handler:4.2.2.Final"        #https://mvnrepository.com/artifact/io.netty/netty-all
jSerialComm = "com.fazecast:jSerialComm:2.11.0"       #https://mvnrepository.com/artifact/com.fazecast/jSerialComm
logback = "ch.qos.logback:logback-classic:1.5.18"     #https://mvnrepository.com/artifact/ch.qos.logback/logback-classic
kotest = "io.kotest:kotest-assertions-core:5.9.1"       #https://mvnrepository.com/artifact/io.kotest/kotest-runner-junit5-jvm
atlantafx = "io.github.mkpaz:atlantafx-base:2.0.1"    #https://mvnrepository.com/artifact/io.github.mkpaz/atlantafx-base
controlsfx = "org.controlsfx:controlsfx:11.2.2"    #https://mvnrepository.com/artifact/org.controlsfx/controlsfx
tornadofx = "no.tornado:tornadofx2:2.0-20250702.061923-2"

[plugins]
