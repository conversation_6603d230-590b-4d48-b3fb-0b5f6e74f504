package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.scene.control.RadioButton
import javafx.scene.layout.VBox
import javafx.stage.Stage
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit

/**
 * Verify that when a RadioButton selection changes, the bound Math label updates accordingly.
 */
class RenderRadioTest : TestCase() {

    @Test
    fun `label should update when radio selection changes`() {
        val profile = XmlConfigParser.parseFromString("""
            <uicfg version="1.0.0">
                <vbox>
                    <radio id="RADIO_MODE">
                        <radiobutton label="Option0" value="2"/>
                        <radiobutton label="Option1" value="1"/>
                        <radiobutton label="Option2" value="8"/>
                    </radio>
                    <math formula="RADIO_MODE"/>
                </vbox>
            </uicfg>
        """.trimIndent())

        setUpFixture { root ->
            // Build UI from profile
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)
            val radioGroup = content.children[0] as VBox
            val label = content.children[1] as javafx.scene.control.Label

            // Get radio buttons from the group
            val radioButtons = radioGroup.children.filterIsInstance<RadioButton>()

            // Initial selection should be 0 (first radio button)
            radioButtons[0].isSelected shouldBe true
            label.text shouldBe "2"

            // Change to index 2
            radioButtons[2].isSelected = true
            radioButtons[0].isSelected shouldBe false
            radioButtons[1].isSelected shouldBe false
            label.text shouldBe "8"

            // Change to index 1
            radioButtons[1].isSelected = true
            radioButtons[0].isSelected shouldBe false
            radioButtons[2].isSelected shouldBe false
            label.text shouldBe "1"
        }
    }

} 