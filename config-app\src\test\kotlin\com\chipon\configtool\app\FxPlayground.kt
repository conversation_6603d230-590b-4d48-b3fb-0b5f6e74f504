package com.chipon.configtool.app

import javafx.geometry.HPos
import javafx.geometry.VPos
import javafx.scene.Node
import javafx.scene.layout.Priority
import javafx.scene.paint.Color
import javafx.stage.Stage
import javafx.stage.StageStyle
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit
import tornadofx.*
import java.util.concurrent.CountDownLatch

@Disabled
class FxPlayground : TestCase() {

    val primaryStage: Stage = FxToolkit.registerPrimaryStage().also {
        it.initStyle(StageStyle.DECORATED)
    }

    @Test
    fun `test grid layout`() {
        setUpFixtureShow {
            it.titledpane(collapsible = false) {
                hgrow = Priority.ALWAYS
                vgrow = Priority.ALWAYS
                maxWidth = Double.MAX_VALUE
                maxHeight = Double.MAX_VALUE
                gridpane {

                    row {
                        button("Button 1").applyLayout()
                        button("Button 2").applyLayout()
                        button("Button 3").applyLayout()
                    }
                    row {
                        button("Button 4").applyLayout()
                        button("Button 5").applyLayout()
                        button("Button 6").applyLayout()
                    }
                }
            }

            primaryStage.show()
        }

        CountDownLatch(1).await()
    }

    @Test
    fun `vertical progress`() {
        setUpFixtureShow {
            it.vbox {
                myprogressbar(true) {
                    prefHeight = 300.0
                    progress = 0.4
                    style {
                        accentColor = Color.GREEN
                    }
                }
            }
        }
    }

    fun <T : Node> T.applyLayout(): T {
        return this.gridpaneConstraints {
            hAlignment = HPos.CENTER
            vAlignment = VPos.CENTER
            hGrow = Priority.ALWAYS
            vGrow = Priority.ALWAYS
        }
    }
} 