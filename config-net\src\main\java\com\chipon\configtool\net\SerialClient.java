package com.chipon.configtool.net;

import com.chipon.configtool.net.transport.JSerialCommChannel;
import com.chipon.configtool.net.transport.JSerialCommChannelConfig;
import com.chipon.configtool.net.transport.JSerialCommDeviceAddress;
import com.fazecast.jSerialComm.SerialPort;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import java.util.function.Consumer;
import org.jetbrains.annotations.Nullable;

/**
 * <AUTHOR>
 */
public class SerialClient extends BaseClient {

    private final SerialPort port;
    private final Consumer<JSerialCommChannelConfig> channelConfiger;
    private Channel channel;

    public SerialClient(SerialPort port, @Nullable Consumer<JSerialCommChannelConfig> channelConfiger) {
        this.port = port;
        this.channelConfiger = channelConfiger;
    }

    public SerialClient(String port) {
        this.port = SerialPort.getCommPort(port);
        this.channelConfiger = null;
    }

    @Override
    public Channel getChannel() {
        return channel;
    }

    @Override
    public Channel connect() throws Exception {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(OIO_EVENT_LOOP_GROUP)
            .channelFactory(() -> {
                JSerialCommChannel channel = new JSerialCommChannel();
                if (channelConfiger != null) {
                    channelConfiger.accept(channel.config());
                }
                return channel;
            })
            .handler(defaultHandlers());

        channel = bootstrap.connect(new JSerialCommDeviceAddress(port.getSystemPortName())).sync().channel();
        return channel;
    }

}
