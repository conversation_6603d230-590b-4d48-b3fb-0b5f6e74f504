package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.scene.control.ComboBox
import javafx.scene.control.Spinner
import javafx.scene.control.ToggleButton
import javafx.scene.layout.VBox
import javafx.stage.Stage
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit

/**
 * <AUTHOR> Wu
 */
class LockOnTest : TestCase() {

    @Test
    fun `checkbox lockon should disable and set values of other controls`() {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <vbox>
                    <checkbox id="LOCK_CHECKBOX" label="Lock Controls" lockon="COMBO_TARGET=6;SPINNER_TARGET=5;RADIO_TARGET=3;TOGGLE_TARGET=1"/>
                    <combo id="COMBO_TARGET">
                        <item label="Combo0" value="2"/>
                        <item label="Combo1" value="4"/>
                        <item label="Combo2" value="6"/>
                    </combo>
                    <spinner id="SPINNER_TARGET" range="0;10" default="0"/>
                    <radio id="RADIO_TARGET">
                        <radiobutton label="Radio0" value="2"/>
                        <radiobutton label="Radio1" value="3"/>
                    </radio>
                    <togglebutton id="TOGGLE_TARGET" label="Toggle"/>
                </vbox>
            </uicfg>
        """.trimIndent()
        )

        setUpFixture { root ->
            // Build UI from profile
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)
            val checkbox = content.children[0] as javafx.scene.control.CheckBox
            val combo = content.children[1] as ComboBox<*>
            val spinner = content.children[2] as Spinner<*>
            val radioGroup = content.children[3] as VBox
            val toggle = content.children[4] as ToggleButton

            // Initially, all controls should be enabled
            combo.isDisabled shouldBe false
            spinner.isDisabled shouldBe false
            toggle.isDisabled shouldBe false

            // Check the checkbox to trigger lock-on
            checkbox.isSelected = true

            // All target controls should now be disabled
            combo.isDisabled shouldBe true
            spinner.isDisabled shouldBe true
            toggle.isDisabled shouldBe true

            // Verify the locked values
            val comboContext = combo.getContext() as UIContext
            val spinnerContext = spinner.getContext() as UIContext
            val toggleContext = toggle.getContext() as UIContext

            comboContext.valueProperty.value.toInt() shouldBe 6
            spinnerContext.valueProperty.value.toInt() shouldBe 5
            toggleContext.valueProperty.value.toInt() shouldBe 1

            // Verify radio group selection
            val radioButtons = radioGroup.toRadioGroup().toggles
            radioButtons[1].isSelected shouldBe true

            // Uncheck the checkbox
            checkbox.isSelected = false

            // Controls should be enabled again
            combo.isDisabled shouldBe false
            spinner.isDisabled shouldBe false
            toggle.isDisabled shouldBe false
        }
    }

    @Test
    fun `combo item lockon should disable and set values of other controls`() {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <vbox>
                    <combo id="COMBO_TARGET">
                        <item label="Combo0" value="0"/>
                        <item label="Combo1" value="1"/>
                        <item label="Combo2" value="2"/>
                    </combo>
                    <combo id="LOCK_COMBO">
                        <item label="Option0" value="0"/>
                        <item label="Option1" value="1" lockon="COMBO_TARGET=2;SPINNER_TARGET=5;RADIO_TARGET=1;TOGGLE_TARGET=1"/>
                        <item label="Option2" value="2"/>
                    </combo>
                    <spinner id="SPINNER_TARGET" range="0;10" default="0"/>
                    <radio id="RADIO_TARGET">
                        <radiobutton label="Radio0" value="0"/>
                        <radiobutton label="Radio1" value="1"/>
                    </radio>
                    <togglebutton id="TOGGLE_TARGET" label="Toggle"/>
                </vbox>
            </uicfg>
        """.trimIndent()
        )

        setUpFixture { root ->
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)
            val lockCombo = content.children[1] as ComboBox<*>
            val combo = content.children[0] as ComboBox<*>
            val spinner = content.children[2] as Spinner<*>
            val radioGroup = content.children[3] as VBox
            val toggle = content.children[4] as ToggleButton

            // Initially, all controls should be enabled
            combo.isDisabled shouldBe false
            spinner.isDisabled shouldBe false
            toggle.isDisabled shouldBe false

            // Select the combo item with lockon (Option1)
            lockCombo.selectionModel.select(1)

            // All target controls should now be disabled
            combo.isDisabled shouldBe true
            spinner.isDisabled shouldBe true
            toggle.isDisabled shouldBe true

            // Verify the locked values
            val comboContext = combo.getContext() as UIContext
            val spinnerContext = spinner.getContext() as UIContext
            val toggleContext = toggle.getContext() as UIContext

            comboContext.valueProperty.value.toInt() shouldBe 2
            spinnerContext.valueProperty.value.toInt() shouldBe 5
            toggleContext.valueProperty.value.toInt() shouldBe 1

            // Verify radio group selection
            val radioButtons = radioGroup.toRadioGroup().toggles
            radioButtons[1].isSelected shouldBe true

            // Select a different combo item (Option0)
            lockCombo.selectionModel.select(0)

            // Controls should be enabled again
            combo.isDisabled shouldBe false
            spinner.isDisabled shouldBe false
            toggle.isDisabled shouldBe false
        }
    }

    @Test
    fun `combo lockon test2`() {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <vbox>
                    <combo id="CTRL.GPIO_CTRL_2_0" default="0">
                        <item label="Off" value="4" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                        <item label="Failure Output" value="0" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                        <item label="High-Side Timer" value="3" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                        <item label="Wake Input" value="5"/>
                        <item label="Low-Side PWM" value="6" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                        <item label="High-Side PWM" value="7" lockon="CTRL.WK_PUPD_CTRL_7_6=0"/>
                    </combo>
                    <combo id="CTRL.WK_PUPD_CTRL_7_6">
                        <item label="None"/>
                        <item label="Pull-down"/>
                        <item label="Pull-up"/>
                        <item label="Automatic"/>
                    </combo>
                </vbox>
            </uicfg>
        """.trimIndent()
        )

        setUpFixture { root ->
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)

            val gpioCombo = content.children[0] as ComboBox<*>
            val pullDeviceCombo = content.children[1] as ComboBox<*>

            pullDeviceCombo.isDisabled shouldBe false
            gpioCombo.selectionModel.selectedIndex shouldBe 1

            // Select "Off" option (value=4) which has lockon="CTRL.WK_PUPD_CTRL_7_6=0"
            gpioCombo.selectionModel.select(0) // "Off" is the first item

            // Pull device combo should now be disabled and set to value 0 (None)
            pullDeviceCombo.isDisabled shouldBe true
            val pullDeviceContext = pullDeviceCombo.getContext() as UIContext
            pullDeviceContext.valueProperty.value.toInt() shouldBe 0

            // Select "Wake Input" option (value=5) which has no lockon
            gpioCombo.selectionModel.select(3) // "Wake Input" is the 4th item (index 3)

            // Pull device combo should be enabled again
            pullDeviceCombo.isDisabled shouldBe false

            // Select "Low-Side PWM" option (value=6) which has lockon="CTRL.WK_PUPD_CTRL_7_6=0"
            gpioCombo.selectionModel.select(4) // "Low-Side PWM" is the 5th item (index 4)

            // Pull device combo should be disabled again and set to value 0
            pullDeviceCombo.isDisabled shouldBe true
            pullDeviceContext.valueProperty.value.toInt() shouldBe 0
        }
    }
}