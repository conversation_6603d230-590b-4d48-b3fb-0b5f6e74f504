package com.chipon.configtool.core.model

import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonMappingException
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

val DEFAULT_SIZE = Size(-1, -1)

/**
 * Custom Size type to handle "width;height" format
 */
@JsonDeserialize(using = SizeDeserializer::class)
data class Size(var width: Int = -1, var height: Int = -1) {

    constructor(width: Int) : this() {
        this.width = width
        this.height = width
    }

    @JsonValue
    override fun toString(): String = "$width;$height"
}

class SizeDeserializer : JsonDeserializer<Size>() {
    override fun deserialize(p: <PERSON>sonPars<PERSON>, ctxt: DeserializationContext): Size {
        val value = p.valueAsString
        return try {
            parse(value)
        } catch (e: Exception) {
            throw JsonMappingException.from(p, "Invalid size format: $value", e)
        }
    }

    fun parse(sizeString: String): Size {
        if (";" in sizeString) {
            val parts = sizeString.split(";")
            if (parts.size != 2) {
                throw IllegalArgumentException("Size must be in format 'width;height' or 'value', got: $sizeString")
            }
            val width = parts[0].toIntOrNull() ?: throw IllegalArgumentException("Invalid width: ${parts[0]}")
            val height = parts[1].toIntOrNull() ?: throw IllegalArgumentException("Invalid height: ${parts[1]}")

            if (width < -1 || height < -1) {
                throw IllegalArgumentException("Width and height must be positive, got: $width;$height")
            }
            return Size(width, height)
        }
        val value = sizeString.toIntOrNull() ?: throw IllegalArgumentException("Invalid value: $sizeString")
        return Size(value)
    }
}