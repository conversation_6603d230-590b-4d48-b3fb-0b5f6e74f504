package com.chipon.configtool.app

import atlantafx.base.theme.Styles
import ch.qos.logback.classic.LoggerContext
import com.chipon.configtool.app.model.AppConfig
import com.chipon.configtool.app.views.EmptyView
import com.chipon.configtool.app.views.ProfileView
import com.chipon.configtool.app.views.SettingsView
import com.chipon.configtool.core.logger
import com.chipon.configtool.core.model.ProfileRoot
import com.chipon.configtool.core.parser.XmlConfigParser
import com.chipon.configtool.net.SerialClient
import com.fazecast.jSerialComm.SerialPort
import javafx.application.Platform
import javafx.geometry.Insets
import javafx.geometry.Pos
import javafx.stage.StageStyle
import org.kordamp.ikonli.materialdesign2.MaterialDesignC
import org.slf4j.LoggerFactory
import tornadofx.*
import java.io.File

/**
 * <AUTHOR> Wu
 */
class HexonWorkspace : Workspace(APP_NAME) {

    val serialPort = objectProperty<SerialPort>()

    init {
        //init theme
        applyTheme(app.config.string(AppConfig.THEME_KEY))
        with(bottomDrawer) {
            contextMenu.items.clear()
            item("Profiles") {
                hbox {
                    padding = Insets(20.0)
                    spacing = 10.0
                    File(PROJECT_ROOT, "profiles").listFiles()
                        .filter { it.name.endsWith(".xml") }
                        .forEach {
                            button(it.nameWithoutExtension) {
                                this.styleClass += Styles.BUTTON_OUTLINED
                                action {
                                    loadProfile(it)
                                }
                            }
                        }
                }
            }
            item("Logs") {
                styletextarea {
                    DragResizer.makeResizable(DragResizer.Direction.TOP, this)

                    val context = LoggerFactory.getILoggerFactory() as LoggerContext
                    val appender = ConsoleViewAppender(context, this)
                    appender.start()
                }
            }
        }

        primaryStage.width = AppConfig.width.value
        primaryStage.height = AppConfig.height.value
    }

    override fun onBeforeShow() {
        workspace.dock(EmptyView, true)

        root.top {
            //remove toolbar
            this.children.remove(header)
            hbox {
                styleClass += "my-toolbar"
                menubar {
                    alignment = Pos.CENTER_LEFT
                    menu("File") {
                        item("Save", graphic = icon(MaterialDesignC.CONTENT_SAVE)).action {
                            logger.info("data saved successfully")
                        }
                        item("Save data as...", graphic = icon(MaterialDesignC.CONTENT_SAVE_PLUS)).action {
                        }
                        separator()
                        item("Exit").action {
                            Platform.exit()
                        }
                    }
                    menu("Extra") {
                        item("Load profile").action {
                            val file = chooseFile("Please select a UI profile file", arrayOf(XML_FILTER))
                            if (file.isNotEmpty()) {
                                loadProfile(file[0])
                            }
                        }
                    }
                    menu {
                        label("Settings") {
                            setOnMouseClicked {
                                find<SettingsView>().openModal(resizable = false, stageStyle = StageStyle.UTILITY)
                            }
                        }
                    }
                    menu("Help") {
                        item("About").action {
                            information(null, "Current version :${APP_VERSION}")
                        }
                    }
                }
                spacer { }
                hbox(spacing = 15, Pos.CENTER_LEFT) {
                    padding = insets(horizontal = 20)
                    label("串口连接:")
                    combobox<SerialPort>(serialPort) {
                        setOnShowing {
                            items.setAll(getSerialPorts())
                        }
                    }
                    switch {
                        selectedProperty().addListener { _, _, v ->
                            pseudoClassStateChanged(Styles.STATE_SUCCESS, v)
                            if (v) {
                                client = SerialClient(serialPort.value, serialConfiger)
                                try {
                                    val channel = client!!.connect()
                                    channel.closeFuture().addListener {
                                        isSelected = false
                                    }
                                } catch (e: Exception) {
                                    logger.showError("串口连接失败: ${e.message}")
                                    isSelected = false
                                }
                            } else {
                                try {
                                    client!!.close()
                                } catch (e: Exception) {
                                    logger.showError("关闭串口失败: ${e.message}")
                                }
                            }
                        }
                    }
                }
            }
        }

        AppConfig.width.bind(primaryStage.widthProperty())
        AppConfig.height.bind(primaryStage.heightProperty())
    }

    private fun loadProfile(file: File) {
        try {
            val profile: ProfileRoot
            try {
                profile = XmlConfigParser.parseFromFile(file, false)
            } catch (e: Exception) {
                logger.showError("Bad file format: ${e.message}", e)
                return
            }
            val errors = profile.validate()
            if (errors.isNotEmpty()) {
                logger.showError("Configuration is invalid: ${errors.joinToString("\n")}")
            } else {
                workspace.dock(ProfileView(profile), true)
            }
        } catch (e: Exception) {
            logger.showError("Error render ui from profile: ${e.message}", e)
        }
    }

}
