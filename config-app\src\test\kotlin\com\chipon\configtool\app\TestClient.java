package com.chipon.configtool.app;

import com.chipon.configtool.net.BaseClient;
import io.netty.channel.Channel;

/**
 * <AUTHOR>
 */
public class TestClient extends BaseClient {

    @Override
    public boolean isConnected() {
        return true;
    }

    @Override
    public Channel getChannel() {
        return null;
    }

    @Override
    public Channel connect() {
        return null;
    }
}
