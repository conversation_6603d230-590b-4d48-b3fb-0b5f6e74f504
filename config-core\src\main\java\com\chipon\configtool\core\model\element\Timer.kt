package com.chipon.configtool.core.model.element

import com.chipon.configtool.core.model.BaseElement
import com.chipon.configtool.core.model.IAction
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <timer>
 */
class Timer : BaseElement(), IAction {

    /**
     * milliseconds between timer events.
     */
    var interval: Int? = null

    var oneshot: String? = null

    var run: String? = null

    @JacksonXmlProperty(localName = "action")
    override var actions: List<Action> = emptyList()

    override fun toString(): String {
        return "timer(" +
                "interval=$interval, " +
                "oneshot=$oneshot, " +
                "run=$run, " +
                "actions=$actions" +
                ")"
    }

}
