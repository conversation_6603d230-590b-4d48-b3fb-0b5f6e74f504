package com.chipon.configtool.core.model

/**
 * Base class for container elements with common container attributes.
 */
abstract class BaseContainer : BaseElement(), IColorable {

    override var color: String? = null

    var children: MutableList<BaseElement> = mutableListOf()

    override fun toString(): String {
        return "${getTagName()} ${super.toString()}"
    }
}

/**
 * <tab>
 */
class TabContainer : BaseContainer(), IDisplayable {
    override lateinit var label: String
}

/**
 * <group>
 */
class GroupContainer : BaseContainer(), IDisplayable {
    override lateinit var label: String
}

/**
 * <grid>
 */
class GridContainer : BaseContainer() {

    var columns: Int = 2

    override fun toString(): String {
        return "grid(" +
                "columns=$columns" +
                ")" +
                " ${super.toString()}"
    }
}

/**
 * <hbox>
 */
class HorizontalContainer : BaseContainer()

/**
 * <vbox>
 */
class VerticalContainer : BaseContainer()
