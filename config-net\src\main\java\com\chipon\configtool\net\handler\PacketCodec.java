package com.chipon.configtool.net.handler;

import com.chipon.configtool.core.LoggerKt;
import com.chipon.configtool.net.packet.IPayload;
import com.chipon.configtool.net.packet.Packet;
import com.chipon.configtool.net.packet.Ping;
import com.chipon.configtool.net.packet.Pong;
import com.chipon.configtool.net.packet.ReadPayload;
import com.chipon.configtool.net.packet.StatusPayload;
import com.chipon.configtool.net.packet.UnknownPayload;
import com.chipon.configtool.net.packet.VarList;
import com.chipon.configtool.net.packet.WritePayload;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageCodec;
import java.util.ArrayList;
import java.util.List;

/**
 * Encodes {@link Packet} to bytes and decodes bytes back to {@link Packet} according to the protocol defined in {@code protocol.md}.
 * codec now expects caller to supply correct seq; codec itself is stateless
 */
public class PacketCodec extends ByteToMessageCodec<Packet> {

    private static final byte SOF = (byte) 0xAA;
    private static final byte EOF = (byte) 0x55;


    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        // We might have multiple frames; search for SOF and EOF
        while (true) {
            int readable = in.readableBytes();
            if (readable < 5) { // minimal frame length
                return;
            }

            // align to SOF
            int sofIndex = in.readerIndex();
            while (in.getByte(sofIndex) != SOF) {
                sofIndex++;
                if (sofIndex >= in.writerIndex()) {
                    // No SOF in buffer
                    in.readerIndex(sofIndex);
                    return;
                }
            }
            in.readerIndex(sofIndex);

            // look for EOF

            int eofIndex = -1;
            //SOF之后最少4个byte
            for (int i = sofIndex + 4; i < in.writerIndex(); i++) {
                if (in.getByte(i) == EOF) {
                    eofIndex = i;
                    break;
                }
            }
            if (eofIndex == -1) {
                // wait for more data
                return;
            }

            int frameLength = eofIndex - sofIndex + 1;
            if (in.readableBytes() < frameLength) {
                return; // not enough bytes yet
            }

            ByteBuf frame = in.readRetainedSlice(frameLength);

            try {
                Packet packet = parseFrame(frame);
                if (packet != null) {
                    out.add(packet);
                }
            } finally {
                frame.release();
            }
        }
    }

    @Override
    protected void encode(ChannelHandlerContext ctx, Packet msg, ByteBuf out) {
        // Prepare payload first so that we can calculate checksum
        out.writeByte(SOF);
        out.writeByte(msg.seq);
        out.writeByte(msg.getCmd());

        ByteBuf payloadBuf = ctx.alloc().buffer();

        IPayload payload = msg.payload;
        if (payload instanceof Ping p) {
            payloadBuf.writeByte(p.getData());
        } else if (payload instanceof Pong p) {
            payloadBuf.writeByte(p.getData());
        } else if (payload instanceof WritePayload w) {
            payloadBuf.writeShort(w.getLength());

            for (VarList v : w.getData()) {
                payloadBuf.writeShort(v.getAddress());
                payloadBuf.writeByte(v.getSize());
                short sizeInt = v.getSize();
                // write data in big-endian order, msb first
                for (int i = sizeInt - 1; i >= 0; i--) {
                    payloadBuf.writeByte((byte) ((v.getData() >> (i * 8)) & 0xFF));
                }
            }
        } else if (payload instanceof ReadPayload r) {
            payloadBuf.writeShort(r.getLength());
            for (Integer address : r.getAddress()) {
                payloadBuf.writeShort(address);
            }
        } else if (payload instanceof StatusPayload s) {
            payloadBuf.writeByte(s.getCode());
        } else if (payload instanceof UnknownPayload p) {
            payloadBuf.writeBytes(p.getData());
        }

        byte checksum = 0;
        for (int i = payloadBuf.readerIndex(); i < payloadBuf.writerIndex(); i++) {
            checksum ^= payloadBuf.getByte(i);
        }

        out.writeBytes(payloadBuf);
        out.writeByte(checksum);
        out.writeByte(EOF);

        payloadBuf.release();
    }

    private Packet parseFrame(ByteBuf frame) {
        // Frame layout: SOF|SEQ|CMD|PAYLOAD|CHECKSUM|EOF
        if (frame.readByte() != SOF) {
            return null; // invalid start
        }

        short seq = frame.readUnsignedByte();
        short cmd = frame.readUnsignedByte();

        // payload is whatever remains minus checksum and EOF
        int payloadLength = frame.readableBytes() - 2;
        ByteBuf payloadBuf = frame.readRetainedSlice(payloadLength);
        byte checksum = frame.readByte();
        frame.readByte();// read eof

        byte calculated = 0;
        for (int i = payloadBuf.readerIndex(); i < payloadBuf.writerIndex(); i++) {
            calculated ^= payloadBuf.getByte(i);
        }
        if (calculated != checksum) {
            payloadBuf.release();
            LoggerKt.getLogger().error("Checksum error: payload {} expected {} != actual {}", ByteBufUtil.hexDump(payloadBuf), calculated, checksum);
            return null;
        }

        IPayload payload = readPayload(cmd, payloadBuf);
        payloadBuf.release();

        return new Packet(seq, payload);
    }

    public static IPayload readPayload(short cmd, ByteBuf in) {
        return switch (cmd) {
            case 0x31 -> new Ping(in.readByte());
            case 0x32 -> new Pong(in.readByte());
            case 0x41 -> readWritePayload(in);
            case 0x51 -> readReadPayload(in);
            case 0x61 -> new StatusPayload(in.readByte());
            default -> new UnknownPayload(cmd, ByteBufUtil.getBytes(in));
        };
    }

    private static WritePayload readWritePayload(ByteBuf in) {
        short totalLen = in.readShort();
        int consumed = 0;
        List<VarList> list = new ArrayList<>();
        while (consumed < totalLen) { // at least address+size
            int addr = in.readUnsignedShort();
            short size = in.readUnsignedByte();
            long data = 0L;
            for (int i = 0; i < size; i++) {
                data = (data << 8) | in.readUnsignedByte();
            }
            consumed += 3 + size;
            list.add(new VarList(addr, size, data));
        }
        return new WritePayload(list);
    }

    private static ReadPayload readReadPayload(ByteBuf in) {
        short totalLen = in.readShort();
        int consumed = 0;
        List<Integer> addresses = new ArrayList<>();
        while (consumed < totalLen) {
            addresses.add((int) in.readShort());
            consumed += 2;
        }
        return new ReadPayload(addresses);
    }
}
