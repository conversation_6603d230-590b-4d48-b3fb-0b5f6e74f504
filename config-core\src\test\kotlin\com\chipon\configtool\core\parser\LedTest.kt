package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.VerticalContainer
import com.chipon.configtool.core.model.control.Led
import com.chipon.configtool.core.model.control.LedColor
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class LedTest {

    @Test
    fun `basic led properties`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Status">
                        <vbox>
                            <led data="CTRL.STATUS" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val tab = root.children[0] as TabContainer
        val group = tab.children[0] as GroupContainer
        val vbox = group.children[0] as VerticalContainer
        val led = vbox.children[0] as Led

        led.data shouldBe "CTRL.STATUS"
        led.bitmask shouldBe "0x01"
        led.oncolor shouldBe LedColor.green
        led.offcolor shouldBe LedColor.red
    }

    @Test
    fun `led default colors when not specified`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Status">
                        <vbox>
                            <led data="FLAG" bitmask="0x02"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        val led = (((root.children[0] as TabContainer).children[0] as GroupContainer)
            .children[0] as VerticalContainer).children[0] as Led

        led.oncolor shouldBe LedColor.green // default
        led.offcolor shouldBe LedColor.grey // default
    }

    @Test
    fun `led validation detects missing attributes`() {
        val xml = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Status">
                        <vbox>
                            <led/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("led data is required") } shouldBe true
        errors.any { it.contains("led bitmask is required") } shouldBe true
    }

    @Test
    fun `led validate detects lateinit var not initialized`() {
        val led = Led()
        // 只设置 bitmask，未设置 data
        led.bitmask = "0x01"
        val errors1 = led.validate()
        errors1.any { it.contains("led data is required") } shouldBe true

        // 只设置 data，未设置 bitmask
        val led2 = Led()
        led2.data = "CTRL.STATUS"
        val errors2 = led2.validate()
        errors2.any { it.contains("led bitmask is required") } shouldBe true

        // 两个都未设置
        val led3 = Led()
        val errors3 = led3.validate()
        errors3.any { it.contains("led data is required") } shouldBe true
        errors3.any { it.contains("led bitmask is required") } shouldBe true
    }

}