<?xml version="1.0" encoding="UTF-8"?>
<uicfg version="1.0.0">
    <svd file="example.svd"/>
    <!-- Main UI container -->
    <tab label="Motion Control Configuration">
        <!-- Variable declarations -->
        <var id="USB.ERROR_CODE"/>
        <var id="LED.GPIO"/>
        <var id="CTRL.M_S_CTRL"/>
        <var id="CTRL.SUP_STAT"/>
        <var id="CTRL.THERM_STAT"/>
        <var id="CTRL.DEV_STAT"/>

        <!-- Initialization timer -->
        <timer id="INIT" interval="1" oneshot="true" run="true">
            <action event="changed" cmd="sendUSB" data="0x01;0x13;0x55;0xAA"/>
            <action event="changed" cmd="sendUSB" data="0x02;0x41;0x0a"/>
            <action event="changed" cmd="sendUSB" data="0x02;0x42"/>
        </timer>

        <!-- Status read timer -->
        <timer id="STATUS_READ" interval="500" oneshot="false" run="true">
            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x41" recdata="?;?;CTRL.SUP_STAT"/>
            <action event="changed" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/>
        </timer>

        <grid columns="2">
            <!-- Left column -->
            <vbox>
                <!-- Connection Status Group -->
                <group label="Connection Status">
                    <grid columns="3">
                        <led data="LED.USB" bitmask="0x01" oncolor="red" offcolor="red" size="25;25"/>
                        <led data="LED.TARGET" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
                        <text label="Firmware Version: 2.2.1"/>
                    </grid>
                </group>
                
                <!-- Control Function Group -->
                <group label="Control Function">
                    <vbox>
                        <!-- Mode Selection -->
                        <group label="Mode">
                            <hbox>
                                <button label="NORMAL" size="55;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0x81"/>
                                </button>
                                <button label="SLEEP" size="55;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x40;0x81"/>
                                </button>
                                <button label="STOP" size="55;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x80;0x81"/>
                                </button>
                            </hbox>
                        </group>
                        
                        <!-- VCC Configuration -->
                        <group label="VCC Configuration">
                            <vbox>
                                <checkbox label="OV Reset active" default="0">
                                    <action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;0x04;0x81"/>
                                </checkbox>
                                <hbox>
                                    <text label="UV Threshold:"/>
                                    <combo default="0">
                                        <item label="VRT1" value="0"/>
                                        <item label="VRT2" value="1"/>
                                        <item label="VRT3" value="2"/>
                                        <item label="VRT4" value="3"/>
                                    </combo>
                                </hbox>
                            </vbox>
                        </group>
                        
                        <!-- Bus Configuration -->
                        <group label="Bus Configuration">
                            <vbox>
                                <hbox>
                                    <text label="CAN:"/>
                                    <combo default="0">
                                        <item label="OFF" value="0"/>
                                        <item label="Wake capable" value="1"/>
                                        <item label="Receive only" value="2"/>
                                        <item label="Normal" value="3"/>
                                    </combo>
                                </hbox>
                                <hbox>
                                    <text label="LIN1:"/>
                                    <combo default="0">
                                        <item label="OFF" value="0"/>
                                        <item label="Wake capable" value="1"/>
                                        <item label="Receive only" value="2"/>
                                        <item label="Normal" value="3"/>
                                    </combo>
                                </hbox>
                                <checkbox label="LIN TXD Time-Out" default="1"/>
                                <checkbox label="LIN Low-Slope" default="0"/>
                            </vbox>
                        </group>
                    </vbox>
                </group>
            </vbox>
            
            <!-- Right column -->
            <vbox>
                <!-- Status Information -->
                <group label="Status Information">
                    <vbox>
                        <!-- Thermal Status -->
                        <group label="Thermal Status">
                            <grid columns="2">
                                <led data="CTRL.THERM_STAT" bitmask="0x04" oncolor="green" size="15;15"/>
                                <text label="TSD2"/>
                                <led data="CTRL.THERM_STAT" bitmask="0x02" oncolor="green" size="15;15"/>
                                <text label="TSD1"/>
                                <led data="CTRL.THERM_STAT" bitmask="0x01" oncolor="green" size="15;15"/>
                                <text label="TPW"/>
                                <button label="CLEAR" size="45;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/>
                                </button>
                            </grid>
                        </group>
                        
                        <!-- Supply Status -->
                        <group label="Supply Status">
                            <grid columns="2">
                                <led data="CTRL.SUP_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
                                <text label="POR"/>
                                <led data="CTRL.SUP_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                <text label="VLIN UV"/>
                                <led data="CTRL.SUP_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
                                <text label="VCC1 OV"/>
                                <led data="CTRL.SUP_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
                                <text label="VCC2 OT"/>
                                <button label="CLEAR" size="45;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC1"/>
                                </button>
                            </grid>
                        </group>
                        
                        <!-- Device Status -->
                        <group label="Device Status">
                            <grid columns="2">
                                <led data="CTRL.DEV_STAT" bitmask="0x80" oncolor="green" size="15;15"/>
                                <text label="DEV STAT1"/>
                                <led data="CTRL.DEV_STAT" bitmask="0x40" oncolor="green" size="15;15"/>
                                <text label="DEV STAT0"/>
                                <led data="CTRL.DEV_STAT" bitmask="0x20" oncolor="green" size="15;15"/>
                                <text label="RO CL HIGH"/>
                                <led data="CTRL.DEV_STAT" bitmask="0x10" oncolor="green" size="15;15"/>
                                <text label="FSI FAIL"/>
                                <button label="CLEAR" size="45;25">
                                    <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC3"/>
                                </button>
                            </grid>
                        </group>
                    </vbox>
                </group>
                
                <!-- Configuration Parameters -->
                <group label="Configuration Parameters">
                    <vbox>
                        <hbox>
                            <text label="PWM Frequency:"/>
                            <doublespinner range="1.0;100.0" step="0.1" default="50.0" suffix="kHz"/>
                        </hbox>
                        <hbox>
                            <text label="Voltage Threshold:"/>
                            <doublespinner range="0.0;5.0" step="0.1" default="3.3" suffix="V"/>
                        </hbox>
                        <vline/>
                        <radio default="0">
                            <radiobutton label="Standard Mode" value="0"/>
                            <radiobutton label="Performance Mode" value="1"/>
                            <radiobutton label="Low Power Mode" value="2"/>
                        </radio>
                    </vbox>
                </group>
            </vbox>
        </grid>
    </tab>
</uicfg> 