package com.chipon.configtool.app.views

import com.chipon.configtool.app.fixedtabpane
import com.chipon.configtool.app.model.AppConfig.serialBaudRate
import com.chipon.configtool.app.model.AppConfig.serialDataBit
import com.chipon.configtool.app.model.AppConfig.serialFlowControl
import com.chipon.configtool.app.model.AppConfig.serialParity
import com.chipon.configtool.app.model.AppConfig.serialStopBit
import com.chipon.configtool.app.model.AppConfig.theme
import javafx.beans.property.ListProperty
import javafx.util.converter.NumberStringConverter
import tornadofx.*

/**
 * <AUTHOR> Wu
 */
class SettingsView : View("Settings") {

    val serialBaudRates: ListProperty<Int> = listProperty(observableListOf(110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 56000, 57600, 115200, 128000, 230400, 256000, 460800, 921600))
    val serialDataBits: ListProperty<Int> = listProperty(observableListOf(5, 6, 7, 8))
    val serialStopBits: ListProperty<Double> = listProperty(observableListOf(1.0, 1.5, 2.0))
    val serialParityList: ListProperty<String> = listProperty(observableListOf("无", "偶", "奇", "标志", "空格"))
    val serialFlowControls: ListProperty<String> = listProperty(observableListOf("无", "Xon/Xoff", "RTS/CTS"))

    override val root = form {
        fixedtabpane {
            tab("串口设置") {
                form {
                    fieldset {
                        field("位/秒") {
                            combobox(serialBaudRate, serialBaudRates) {
                                isEditable = true
                                converter = NumberStringConverter("#")
                            }
                        }
                        field("数据位") {
                            combobox(serialDataBit, serialDataBits)
                        }
                        field("停止位") {
                            combobox(serialStopBit, serialStopBits)
                        }
                        field("奇偶校验") {
                            combobox(serialParity, serialParityList)
                        }
                        field("流控制") {
                            combobox(serialFlowControl, serialFlowControls)
                        }
                    }
                }
            }
            tab("通用设置") {
                form {
                    fieldset {
                        field("主题切换") {
                            combobox(
                                property = theme,
                                values = mutableListOf("默认", "现代")
                            )
                        }
                    }
                }
            }
        }
    }

}
