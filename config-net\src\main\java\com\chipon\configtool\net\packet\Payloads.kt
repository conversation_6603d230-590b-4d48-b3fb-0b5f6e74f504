package com.chipon.configtool.net.packet

import com.chipon.configtool.net.handler.PacketCodec
import io.netty.buffer.Unpooled

/**
 * <AUTHOR> Wu
 */

interface IPayload {
    val cmd: Short //1byte
}

data class Ping(val data: Byte) : IPayload {
    override val cmd: Short = CMD

    companion object {
        const val CMD: Short = 0x31
    }
}

data class Pong(val data: Byte) : IPayload {
    override val cmd: Short = CMD

    companion object {
        const val CMD: Short = 0x32
    }
}

data class WritePayload(
    val data: List<VarList>
) : IPayload {
    override val cmd: Short = CMD

    companion object {
        const val CMD: Short = 0x41
    }

    //2 bytes
    fun getLength(): Int {
        return data.sumOf { 2 + 1 + it.size }
    }
}

data class ReadPayload(
    val address: List<Int> //2 bytes
) : IPayload {
    override val cmd: Short = CMD

    companion object {
        const val CMD: Short = 0x51
    }

    //2bytes
    fun getLength(): Int {
        return address.size * 2
    }
}

data class StatusPayload(
    val code: Short,
) : IPayload {
    override val cmd: Short = CMD

    companion object {
        const val CMD: Short = 0x61
    }
}

class UnknownPayload(override val cmd: Short, val data: ByteArray) : IPayload

data class VarList(
    val address: Int, //2 bytes
    val size: Short,     // 1byte
    val data: Long
)

fun statusCodeToString(code: Byte): String {
    return when (code) {
        0x01.toByte() -> "芯片故障"
        0x02.toByte() -> "上位机数据包解析错误"
        0x03.toByte() -> "数据访问地址错误"
        else -> "UNKNOWN code: $code"
    }
}

fun parsePayload(data: ByteArray): IPayload {
    if (data.size < 2) {
        throw IllegalArgumentException("数据包最少要求cmd+data")
    }
    val cmd = data[0].toShort()
    val buf = Unpooled.wrappedBuffer(data, 1, data.size - 1)
    try {
        return PacketCodec.readPayload(cmd, buf)
    } catch (ex: Exception) {
        throw IllegalArgumentException("Bad payload", ex);
    } finally {
        buf.release();
    }
}