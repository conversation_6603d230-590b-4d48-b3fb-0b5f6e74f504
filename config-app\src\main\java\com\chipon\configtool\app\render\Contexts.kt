package com.chipon.configtool.app.render

import com.chipon.configtool.app.controls.LedControl
import com.chipon.configtool.core.model.BaseContainer
import com.chipon.configtool.core.model.BaseElement
import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.Variable
import com.chipon.configtool.core.model.control.Led
import com.chipon.configtool.core.model.control.UIDoubleSpinner
import com.chipon.configtool.core.model.element.DataType
import com.chipon.configtool.core.model.element.MathDefinition
import com.chipon.configtool.core.model.element.Timer
import com.chipon.configtool.core.parser.TypeMissMatchException
import javafx.animation.Timeline
import javafx.beans.binding.NumberExpression
import javafx.beans.property.DoubleProperty
import javafx.beans.property.IntegerProperty
import javafx.beans.property.LongProperty
import javafx.beans.property.SimpleBooleanProperty
import javafx.scene.Node
import tornadofx.*

fun initProperty(type: DataType, holder: BaseElement? = null): NumberExpression {
    var t = type
    if (holder is BaseUIControl) {
        if (holder is UIDoubleSpinner) {
            t = DataType.double
        }
    }
    return when (t) {
        DataType.int8, DataType.int16, DataType.int32,
        DataType.uint8, DataType.uint16, DataType.uint32 -> intProperty(0)

        DataType.int64, DataType.uint64 -> longProperty(0)
        DataType.double -> doubleProperty(0.0)
    }
}

class ContainerContext(holder: BaseContainer) : BaseContext(holder) {

    override val valueProperty: NumberExpression
        get() = throw UnsupportedOperationException()

    override fun toString(): String {
        return "UIContext $holder"
    }
}

open class VariableContext(holder: Variable) : BaseContext(holder) {
    override val valueProperty: NumberExpression = initProperty(holder.type)

    override fun dataType(): DataType {
        return (holder as Variable).type
    }

}

class MathContext(val math: MathDefinition) : DerivedContext(math) {

    override val valueProperty: NumberExpression = initProperty(math.type)

    override fun dataType(): DataType {
        return (holder as MathDefinition).type
    }

    override fun tooltip(): String {
        return "${holder.getTagName()} id=$id, value=$value, type=${dataType()}"
    }

    fun reportTypeError(op: () -> Number): Number {
        try {
            return op()
        } catch (e: IllegalArgumentException) {
            throw TypeMissMatchException(math, e)
        } catch (e: ClassCastException) {
            throw TypeMissMatchException(math, e)
        }
    }

    override fun doResolve(contexts: Map<String, BaseContext>) {
        val dependency: Array<NumberExpression> = Evaluator.computeDependencies(math.formula, contexts)

        when (valueProperty) {
            is IntegerProperty -> valueProperty.bind(integerBinding(contexts, *dependency) {
                val result = Evaluator.evaluate(math.formula, contexts)
                reportTypeError { DataTypeValidator.validate(result, math.type) }.toInt()
            })

            is LongProperty -> valueProperty.bind(longBinding(contexts, *dependency) {
                val result = Evaluator.evaluate(math.formula, contexts)
                reportTypeError { DataTypeValidator.validate(result, math.type) }.toLong()
            })

            is DoubleProperty -> valueProperty.bind(doubleBinding(contexts, *dependency) {
                val result = Evaluator.evaluate(math.formula, contexts)
                reportTypeError { DataTypeValidator.validate(result, math.type) }.toDouble()
            })
        }
    }

    override fun toString(): String {
        return "MathContext($math)"
    }
}

class UIContext(holder: BaseUIControl, val control: Node) : BaseContext(holder) {

    override val valueProperty: NumberExpression = initProperty(DataType.uint8, holder)

    override fun dataType(): DataType {
        if(holder is UIDoubleSpinner){
            return DataType.double
        }
        return DataType.uint8
    }

    override fun isResolved(): Boolean = true
}

class LedContext(val control: LedControl, val led: Led) : DerivedContext(led) {

    override val valueProperty: IntegerProperty = intProperty(0)

    override fun dataType(): DataType = DataType.uint8

    override fun doResolve(contexts: Map<String, BaseContext>) {
        val dependency: NumberExpression = Evaluator.computeDependencies(led.data, contexts)[0]
        valueProperty.bind(integerBinding(dependency) {
            dependency.value.toInt() and led.getBitmaskValue()
        })

        // LED is ON when the evaluated bitmask is non-zero
        control.stateProperty().bind(valueProperty.greaterThan(0))
    }

    override fun toString(): String {
        return "LedContext($led)"
    }

}

class TimerContext(holder: Timer) : BaseContext(holder) {

    override val valueProperty: NumberExpression
        get() = throw UnsupportedOperationException()

    private val timeline = Timeline()
    val runningProperty = SimpleBooleanProperty(holder.run == "1")

    fun start() { /* ... */
    }

    fun stop() { /* ... */
    }
}