package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.GroupContainer
import com.chipon.configtool.core.model.TabContainer
import com.chipon.configtool.core.model.VerticalContainer
import com.chipon.configtool.core.model.control.UICheckbox
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

/**
 * <AUTHOR> Wu
 */
class CheckboxTest {

    @Test
    fun `test checkbox with false default`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <checkbox label="Disabled Option" default="0"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val profileRoot = XmlConfigParser.parseFromString(xmlContent)
        val checkbox = (profileRoot.children[0] as <PERSON><PERSON><PERSON><PERSON><PERSON>).children[0] as GroupContainer
        val vbox = checkbox.children[0] as VerticalContainer
        val testCheckbox = vbox.children[0] as UICheckbox

        testCheckbox.label shouldBe "Disabled Option"
        testCheckbox.default shouldBe 0
    }

    @Test
    fun `test checkbox with lockon property`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <checkbox lockon="CTRL.WK_CTRL_1[0]=0;CTRL.GPIO_CTRL[2:0]=4;CTRL.WK_PUPD_CTRL[1:0]=0"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val profileRoot = XmlConfigParser.parseFromString(xmlContent, false)
        val checkbox = (profileRoot.children[0] as TabContainer).children[0] as GroupContainer
        val vbox = checkbox.children[0] as VerticalContainer
        val testCheckbox = vbox.children[0] as UICheckbox

        testCheckbox.lockon!!.lockers.size shouldBe 3
        testCheckbox.lockon!!.lockers[0].id shouldBe "CTRL.WK_CTRL_1[0]"
        testCheckbox.lockon!!.lockers[0].state shouldBe 0
        testCheckbox.lockon!!.lockers[1].id shouldBe "CTRL.GPIO_CTRL[2:0]"
        testCheckbox.lockon!!.lockers[1].state shouldBe 4
        testCheckbox.lockon!!.lockers[2].id shouldBe "CTRL.WK_PUPD_CTRL[1:0]"
        testCheckbox.lockon!!.lockers[2].state shouldBe 0
    }
} 