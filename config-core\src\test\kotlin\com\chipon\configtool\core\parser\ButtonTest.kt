package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.UIButton
import com.chipon.configtool.core.model.element.Cmd
import com.chipon.configtool.core.model.element.Event
import com.chipon.configtool.core.parser.XmlConfigParser.mapper
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

/**
 * <AUTHOR> Wu
 */
class ButtonTest {

    @Test
    fun `should parse button with basic properties`() {
        val xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Test Tab">
                    <button label="NORMAL" size="55;25"/>
                </tab>
            </uicfg>
        """.trimIndent()

        val config = XmlConfigParser.parseFromString(xmlContent)
        val button = ((config.children[0] as TabContainer).children[0] as UIButton)

        button.label shouldBe "NORMAL"
        button.size.width shouldBe 55
        button.size.height shouldBe 25
    }

    @Test
    fun `test button actions`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <button label="NORMAL" size="55;25">
                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0x81"/>
            </button>
        """.trimIndent()

        val readValue = mapper.readValue(xml, UIButton::class.java)
        println("Button2 parsed: $readValue")
        println("Label: ${readValue.label}")
        println("Size: ${readValue.size}")
        println("Action: ${readValue.actions}")
        println("Action size: ${readValue.actions.size}")

        readValue.actions.forEach { action ->
            println("Action - event: ${action.event}, cmd: ${action.cmd}, data: ${action.data}")
        }
    }

    @Test
    fun `should parse button with actions and receivedata`() {
        val xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Test Tab">
                    <group label="Thermal Status">
                        <grid columns="2">
                            <button label="CLEAR" size="45;25">
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0xC2"/>
                                <action event="clicked" cmd="sendUSB" data="0x02;0x4C;0x02;0x00;0x42" recdata="?;?;CTRL.THERM_STAT"/>
                            </button>
                        </grid>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val config = XmlConfigParser.parseFromString(xmlContent)
        val button = (((config.children[0] as TabContainer).children[0] as GroupContainer).children[0] as GridContainer).children[0] as UIButton

        button.label shouldBe "CLEAR"
        button.actions.size shouldBe 2

        button.actions[0].event shouldBe Event.clicked
        button.actions[0].cmd shouldBe Cmd.sendUSB
        button.actions[0].data shouldBe "0x02;0x4A;0x02;0x00;0xC2"
        button.actions[0].recdata.shouldBeNull()

        button.actions[1].event shouldBe Event.clicked
        button.actions[1].cmd shouldBe Cmd.sendUSB
        button.actions[1].data shouldBe "0x02;0x4C;0x02;0x00;0x42"
        button.actions[1].recdata shouldBe "?;?;CTRL.THERM_STAT"
    }

    @Test
    fun `should parse button in nested container structure`() {
        val xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Motion Control Configuration">
                    <group label="Control Function">
                        <vbox>
                            <group label="Mode">
                                <hbox>
                                    <button label="NORMAL" size="55;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0x81"/>
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x00;0x81"/>
                                    </button>
                                    <button label="STOP" size="55;25">
                                        <action event="clicked" cmd="sendUSB" data="0x02;0x4A;0x02;0x80;0x81"/>
                                    </button>
                                </hbox>
                            </group>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val config = XmlConfigParser.parseFromString(xmlContent)
        val hbox = ((((config.children[0] as TabContainer).children[0] as GroupContainer).children[0] as VerticalContainer).children[0] as GroupContainer).children[0] as HorizontalContainer

        val normalButton = hbox.children[0] as UIButton
        normalButton.label shouldBe "NORMAL"
        normalButton.size.width shouldBe 55
        normalButton.actions[0].data shouldBe "0x02;0x4A;0x02;0x00;0x81"
        normalButton.actions.size shouldBe 2

        val stopButton = hbox.children[1] as UIButton
        stopButton.label shouldBe "STOP"
        stopButton.actions[0].data shouldBe "0x02;0x4A;0x02;0x80;0x81"
    }

    @Test
    fun `button color passes validation`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Main">
                    <button label="OK" color="#123ABC" size="10;10"/>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml)
        root.validate().shouldBeEmpty()
    }

    @Test
    fun `invalid button color fails validation`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Main">
                    <button label="Fail" color="#ZZZZZZ" size="10;10"/>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("invalid color format") }.shouldBe(true)
    }
} 