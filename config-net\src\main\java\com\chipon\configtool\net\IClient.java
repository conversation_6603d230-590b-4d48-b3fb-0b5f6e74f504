package com.chipon.configtool.net;

import com.chipon.configtool.net.packet.IPayload;
import io.netty.channel.Channel;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.oio.OioEventLoopGroup;

/**
 * <AUTHOR>
 */
@SuppressWarnings("deprecation")
public interface IClient extends AutoCloseable {

    //事件循环, 线程池
    EventLoopGroup OIO_EVENT_LOOP_GROUP = new OioEventLoopGroup();

    Channel connect() throws Exception;

    boolean isConnected();

    void write(IPayload payload);
}
