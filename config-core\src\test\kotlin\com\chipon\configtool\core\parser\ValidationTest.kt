package com.chipon.configtool.core.parser

import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ValidationTest {

    @Test
    fun `aggregated validation returns displayable and colorable errors`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Main">
                    <!-- label blank & invalid colour -->
                    <button label="  " color="#GGGGGG" size="10;10"/>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()

        // We expect both kinds of errors to be reported
        val expected = listOf("label is required", "invalid color format")
        expected.forEach { exp ->
            errors.any { it.contains(exp) }.shouldBe(true)
        }
    }
} 