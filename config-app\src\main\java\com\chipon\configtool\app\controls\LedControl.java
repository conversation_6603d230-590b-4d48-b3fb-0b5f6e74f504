package com.chipon.configtool.app.controls;

import java.util.List;
import java.util.Objects;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.css.CssMetaData;
import javafx.css.Styleable;
import javafx.css.StyleablePropertyFactory;
import javafx.scene.control.Control;
import javafx.scene.control.Skin;
import javafx.scene.paint.Color;

public class LedControl extends Control {

    private static final StyleablePropertyFactory<LedControl> FACTORY = new StyleablePropertyFactory<>(Control.getClassCssMetaData());

    private final Color offColor;
    private final Color onColor;
    private final BooleanProperty state;

    private static String userAgentStyleSheet;

    public LedControl(Color offColor, Color onColor) {
        getStyleClass().add("led");
        this.state = new SimpleBooleanProperty(this, "state", false);
        this.offColor = offColor;
        this.onColor = onColor;
    }

    public boolean getState() {
        return state.get();
    }

    public void setState(final boolean state) {
        this.state.set(state);
    }

    public BooleanProperty stateProperty() {
        return state;
    }

    public Color getColor() {
        return state.get() ? onColor : offColor;
    }

    public Color getOnColor() {
        return onColor;
    }

    public Color getOffColor() {
        return offColor;
    }

    // ******************** Style related *************************************
    @Override
    protected Skin<?> createDefaultSkin() {
        return new LedSkin(LedControl.this);
    }

    @Override
    public String getUserAgentStylesheet() {
        if (userAgentStyleSheet == null) {
            userAgentStyleSheet = Objects.requireNonNull(LedControl.class.getResource("led.css")).toExternalForm();
        }
        return userAgentStyleSheet;
    }

    @Override
    public List<CssMetaData<? extends Styleable, ?>> getControlCssMetaData() {
        return FACTORY.getCssMetaData();
    }
}