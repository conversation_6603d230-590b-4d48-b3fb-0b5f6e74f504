package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

/**
 * <AUTHOR>
 */
class RenderTest : TestCase() {

    @Test
    fun `test render`() {
        val profile = XmlConfigParser.parseFromFile(HEXON_TLE94x1)

        setUpFixture { root ->
            val env = UIRenderer.buildView(profile, root)

            val ledUsbCtx: MathContext = env.dependentContexts
                .filterIsInstance<MathContext>()
                .first { it.holder.id == "LED.USB" }

            ledUsbCtx.value.toInt() shouldBe 0
        }
    }

    @Test
    fun `test render tab`() {
        val profile = XmlConfigParser.parseFromString(
            """
            <uicfg version="1.0.0">
                <tab label="tab">
                    <var id="CTRL.M_S_CTRL_7_6"/>
                    <math visible="false" id="USB.M_S_CTRL" formula="CTRL.M_S_CTRL_7_6*0x40">
                        <action event="changed" cmd="sendUSB" data="0x02;0x4A;0x02;USB.M_S_CTRL;0x81"/>
                    </math>
                    <vbox>
                        <radio id="RADIO_MODE">
                            <radiobutton label="Option0" value="2"/>
                            <radiobutton label="Option1" value="1"/>
                            <radiobutton label="Option2" value="8"/>
                        </radio>
                    </vbox>
                </tab>
            </uicfg>
        """.trimIndent())

        setUpFixture { root ->
            // Build UI from profile
            UIRenderer.buildView(profile, root)
        }
    }

}