package com.chipon.configtool.app

import atlantafx.base.controls.ToggleSwitch
import com.chipon.configtool.app.controls.LedControl
import com.chipon.configtool.app.controls.TitledBorder
import com.chipon.configtool.app.controls.VerticalProgressBar
import com.chipon.configtool.core.model.control.LedColor
import javafx.beans.binding.Bindings
import javafx.beans.binding.NumberBinding
import javafx.beans.property.BooleanProperty
import javafx.collections.ListChangeListener
import javafx.event.EventTarget
import javafx.geometry.Pos
import javafx.scene.control.ContextMenu
import javafx.scene.control.Tab
import javafx.scene.control.ProgressBar
import javafx.scene.control.TabPane
import javafx.scene.control.Toggle
import javafx.scene.layout.Region
import javafx.scene.layout.VBox
import javafx.scene.paint.Color
import org.fxmisc.flowless.VirtualizedScrollPane
import org.fxmisc.richtext.StyleClassedTextArea
import org.kordamp.ikonli.Ikon
import org.kordamp.ikonli.javafx.FontIcon
import tornadofx.*

/**
 * DSL for javafx component
 *
 * <AUTHOR> Wu
 */

fun EventTarget.fixedtabpane(op: TabPane.() -> Unit = {}) =
    TabPane().attachTo(this, op).apply {
        tabClosingPolicy = TabPane.TabClosingPolicy.UNAVAILABLE

        // 设置TabPane的尺寸策略，让它根据当前选中的Tab内容调整尺寸
        prefWidth = Region.USE_COMPUTED_SIZE
        prefHeight = Region.USE_COMPUTED_SIZE
        minWidth = Region.USE_COMPUTED_SIZE
        minHeight = Region.USE_COMPUTED_SIZE

        // 定义共用的Tab尺寸调整函数
        fun adjustTabPaneSize(tab: Tab?) {
            if (tab?.content != null) {
                runLater {
                    // 获取Tab内容的首选尺寸
                    val content = tab.content
                    if (content is Region) {
                        // 确保内容先完成布局计算
                        content.autosize()
                        content.applyCss()
                        content.layout()

                        // 获取内容的实际尺寸需求
                        val contentWidth = content.prefWidth(-1.0)
                        val contentHeight = content.prefHeight(-1.0)

                        // 计算TabPane需要的总尺寸（包括Tab头部区域）
                        val tabHeaderHeight = 30.0 // Tab头部大约高度
                        val padding = 10.0 // 内边距

                        // 设置TabPane的尺寸以适应当前Tab内容
                        if (contentWidth > 0) {
                            prefWidth = contentWidth + padding
                        }
                        if (contentHeight > 0) {
                            prefHeight = contentHeight + tabHeaderHeight + padding
                        }
                    }

                    // 强制重新计算TabPane的尺寸
                    autosize()
                    requestLayout()

                    // 通知父容器重新布局
                    parent?.requestLayout()
                }
            }
        }

        // 添加Tab选择监听器，当切换Tab时动态调整TabPane尺寸
        selectionModel.selectedItemProperty().addListener { _, _, newTab ->
            adjustTabPaneSize(newTab)
        }

        // 监听tabs列表变化，为初始Tab设置正确尺寸
        tabs.addListener(ListChangeListener { change ->
            while (change.next()) {
                if (change.wasAdded()) {
                    // 当第一个Tab添加时，立即调整尺寸
                    runLater {
                        adjustTabPaneSize(selectionModel.selectedItem)
                    }
                }
            }
        })
    }

fun EventTarget.container(op: VBox.() -> Unit = {}): VBox {
    val vBox = VBox()
    vBox.padding = insets(10.0)
    vBox.alignment = Pos.TOP_RIGHT
    opcr(this, vBox, op)
    return vBox
}

fun EventTarget.groupcontainer(title: String? = null, op: (TitledBorder).() -> Unit = {}): TitledBorder {
    val titledPane = TitledBorder()
    opcr(this, titledPane, op)
    return titledPane
}

fun EventTarget.led(offColor: Color, onColor: Color, op: (LedControl).() -> Unit = {}): LedControl {
    val led = LedControl(offColor, onColor)
    opcr(this, led, op)
    return led
}

fun EventTarget.myprogressbar(vertical: Boolean, op: ProgressBar.() -> Unit = {}): ProgressBar {
    val progressBar = if (vertical) VerticalProgressBar() else ProgressBar()
    progressBar.attachTo(this, op)
    return progressBar
}

fun EventTarget.styletextarea(op: StyleClassedTextArea.() -> Unit = {}):StyleClassedTextArea {
    val area = StyleClassedTextArea()
    VirtualizedScrollPane(area).attachTo(this)
    with(area) {
        style {
            fontFamily = "Consolas"
        }
        isWrapText = true
        isEditable = false
        contextMenu = ContextMenu().apply {
            item("Clear") {
                action {
                    <EMAIL>()
                }
            }
        }
        op()
    }
    return area
}

fun Toggle.groupValue(): Any? {
    return properties["tornadofx.toggleGroupValue"]
}

fun LedColor.fxColor(): Color {
    return when (this) {
        LedColor.green -> Color.web("#00ff00")
        LedColor.red -> Color.web("#ff471a")
        LedColor.grey -> Color.web("#cccccc")
    }
}

fun EventTarget.switch(text: String? = null, op: ToggleSwitch.() -> Unit = {}) = ToggleSwitch(text).attachTo(this, op) {
}

fun BooleanProperty.toIntBinding(): NumberBinding = Bindings.`when`(this).then(1).otherwise(0)

/**
 * https://kordamp.org/ikonli/cheat-sheet-materialdesign2.html
 */
fun icon(iconCode: Ikon): FontIcon {
    return FontIcon(iconCode)
}
