package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IDefaultable
import com.chipon.configtool.core.model.IDisplayable
import com.chipon.configtool.core.model.IValidatable
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <radio>
 */
class UIRadio : BaseUIControl(), IDefaultable<Int>, IValidatable {

    /**
     * The default selected item index, start from 0. default to 0
     */
    override var default: Int = 0

    @JacksonXmlProperty(localName = "radiobutton")
    var radioButtons: List<RadioButton> = emptyList()

    override fun validate(): List<String> {
        //normalize default value
        if (radioButtons.all { it.value == 0 }) {
            radioButtons.forEachIndexed { index, radioButton ->
                radioButton.value = index
            }
        }

        //validate
        val errors = mutableListOf<String>()
        if (radioButtons.isEmpty()) {
            errors += "Radio has no <radiobutton> elements defined"
        }
        if (default < 0 || default >= radioButtons.size) {
            errors += "default index out of range: $default, expected 0..${radioButtons.size - 1}"
        }
        return errors
    }

    override fun toString(): String {
        val defaultInfo = "default=$default"
        val buttonsInfo = "buttons=${radioButtons.size}"
        val info = listOf(defaultInfo, buttonsInfo).filter { it.isNotEmpty() }.joinToString(", ")
        return "${this::class.simpleName}($info)"
    }
}

/**
 * <radiobutton> Represents a radio button within a radio group.
 */
data class RadioButton(
    override var label: String,
    var value: Int
) : IDisplayable
