package com.chipon.configtool.core.model

import com.chipon.configtool.core.model.element.Action
import com.chipon.configtool.core.model.element.DataType
import com.chipon.configtool.core.parser.TAG_MAP
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

interface IAction {
    var actions: List<Action>
}

/**
 * Can disable another element
 */
interface ILocker {
    /**
     * only apply to [combo, radio, spinner, doublespinner, toggle(checkbox,switch,togglebutton)
     */
    var lockon: LockOn?

}

/**
 * wrapper class
 */
data class LockOn(
    var lockers: List<Locker>
)

/**
 * 被锁定的目标
 */
data class Locker(
    val id: String,
    /**
     * lock to the state
     */
    val state: Int = 0,
)

/**
 * with default state
 */
interface IDefaultable<T> {
    var default: T
}

interface IDisplayable {
    var label: String

    fun validateLabel(): List<String> {
        return try {
            if (this.label.isBlank()) listOf("label is required") else emptyList()
        } catch (e: UninitializedPropertyAccessException) {
            listOf("label is required")
        }
    }
}

private val HEX_COLOR_REGEX = Regex("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$")

interface IColorable {

    /**
     * HEX format "#FFFFFF"
     */
    var color: String?

    fun validateColor(): List<String> {
        val errors = mutableListOf<String>()
        if (color != null && !HEX_COLOR_REGEX.matches(color!!)) {
            errors += "invalid color format: $color, expected #RRGGBB or #RGB"
        }
        return errors
    }
}

interface IValidatable {
    /**
     * return error message if invalid
     */
    fun validate(): List<String>
}

/**
 * Base class for all UI elements with common properties.
 */
abstract class BaseElement {

    open var id: String? = null

    fun getTagName(): String {
        return TAG_MAP.inverse()[this.javaClass] ?: throw IllegalArgumentException("Unmapped element type: ${this.javaClass.name}")
    }

    override fun toString(): String {
        return if (id != null) "(id=$id)" else ""
    }
}

/**
 * Base class for UI control elements.
 */
abstract class BaseUIControl : BaseElement() {
    /**
     * 格式: "宽;高" 或者 "宽高"
     */
    @JacksonXmlProperty(isAttribute = true)
    open var size: Size = DEFAULT_SIZE

    open var readonly: Int = 0

}

/**
 * <var>
 */
class Variable : BaseElement() {
    var type: DataType = DataType.uint8
}

/**
 * <vline>
 */
class VLine : BaseUIControl(), IColorable {
    override var color: String? = null
}

/**
 * <hline>
 */
class HLine : BaseUIControl(), IColorable {
    override var color: String? = null
}