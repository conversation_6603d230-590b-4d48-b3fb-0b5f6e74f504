package com.chipon.configtool.app.render

import atlantafx.base.theme.Styles
import com.chipon.configtool.app.*
import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.*
import com.chipon.configtool.core.model.element.MathDefinition
import com.chipon.configtool.core.model.element.Timer
import javafx.event.EventHandler
import javafx.event.EventTarget
import javafx.geometry.Orientation
import javafx.geometry.Pos
import javafx.geometry.VPos
import javafx.scene.Node
import javafx.scene.control.*
import javafx.scene.layout.Priority
import javafx.scene.layout.Region
import javafx.util.StringConverter
import tornadofx.*
import org.controlsfx.tools.Borders
import javafx.scene.layout.VBox

/**
 * Render ProfileRoot model to JavaFX components
 *
 * <AUTHOR> Wu
 */
object UIRenderer {

    const val DEFAULT_GAP = 2
    const val FAKE_RADIO_KEY = "FAKE_RADIO"

    class RenderEnv {
        val defineContexts = linkedMapOf<String, BaseContext>()
        val dependentContexts = arrayListOf<BaseContext>()
    }

    /**
     * Main entry point to build the view from ProfileRoot
     */
    fun buildView(profile: ProfileRoot, parent: ScrollPane): RenderEnv {
        val env = RenderEnv()
        if (profile.children.isNotEmpty() && profile.children[0] is TabContainer) {
            //render tabs
            val tabPane = parent.fixedtabpane {}
            profile.children.forEach { child ->
                renderElement(child, tabPane, env)
            }
        } else {
            renderElement(profile.children[0], parent, env)
        }

        for (context in env.dependentContexts) {
            context.resolve(env.defineContexts)
        }

        for (context in env.defineContexts.values) {
            if (context is UIContext) {
                val element = context.holder
                val control = context.control
                when (control) {
                    is Toggle -> bindLockOn(control.selectedProperty(), (element as? ILocker)?.lockon, env.defineContexts)
                    is CheckBox -> bindLockOn(control.selectedProperty(), (element as? ILocker)?.lockon, env.defineContexts)
                    is ComboBox<*> -> bindComboLockOn(control, env.defineContexts)
                }
            }
        }

        return env
    }

    /**
     * Renders any BaseElement to the appropriate JavaFX component
     *
     * 因为递归的机制,渲染顺序如下:
     * 1. pre渲染container的context
     * 2. onProcess渲染<var> <math> <timer>
     * 3. post渲染UI组件的context
     */
    private fun renderElement(element: BaseElement, container: EventTarget, env: RenderEnv): Any? {
        val result: Any? = when (element) {
            // Containers
            is TabContainer -> renderTabContainer(element, container as TabPane, env)
            is GroupContainer -> renderGroupContainer(element, container, env)
            is GridContainer -> renderGridContainer(element, container, env)
            is HorizontalContainer -> renderHBoxContainer(element, container, env)
            is VerticalContainer -> renderVBoxContainer(element, container, env)
            // UI Controls
            is UIButton -> renderButton(element, container)
            is UISwitch -> renderSwitch(element, container)
            is UIToggle -> renderToggleButton(element, container)
            is UICheckbox -> renderCheckbox(element, container)
            is UICombo -> renderCombo(element, container)
            is UIRadio -> renderRadio(element, container)
            is UIText -> renderText(element, container)
            is UITextInput -> renderTextInput(container)
            is Led -> renderLed(element, container)
            is UISpinner -> renderSpinner(element, container)
            is UIDoubleSpinner -> renderDoubleSpinner(element, container)
            is VLine -> renderVLine(container)
            is HLine -> renderHLine(container)
            is UIProgressBar -> renderProgressBar(element, container)

            // Elements
            is MathDefinition -> renderMath(element, container, env)
            is Timer -> renderTimer(element, env)
            is Variable -> renderVariable(element, env) // Variables are data-only, no UI

            else -> {
                throw NotImplementedError("Unknown element type: ${element::class.simpleName}")
            }
        }

        if (result is EventTarget) {
            if (element !is BaseContainer) {
                //后置设置ui control
                result.setContext(element, env)
            }

            if (result is Node) {
                setupCommon(result, element)
            }

            if (result is Control && element !is UIText && element !is BaseContainer) {
                val tooltip = Tooltip()
                tooltip.onShown = EventHandler {
                    tooltip.text = result.getContext()!!.tooltip()
                }
                result.tooltip = tooltip
            }
        }

        if (element is IAction) {
            element.registerActions(result, env.defineContexts)
        }

        return result
    }

    /* ---------------- Container Renderers ---------------- */

    private fun renderTabContainer(element: TabContainer, parent: TabPane, env: RenderEnv): Tab {
        return parent.tab(element.label) {
            setContext(element, env)

                renderContainerContent(element, this, env)
        }
    }

    private fun renderGroupContainer(element: GroupContainer, parent: EventTarget, env: RenderEnv): Node {
        // 使用 ControlsFX 的 Borders 构建带有标题的线型边框
        // 创建内部内容容器
        val contentPane = VBox(DEFAULT_GAP.toDouble()).apply {
            maxWidth = Double.MAX_VALUE
            maxHeight = Double.MAX_VALUE
        }

        // 先渲染子内容到内部容器
        renderContainerContent(element, contentPane, env)

        // 构建带标题的线型边框
        val borderedNode = Borders.wrap(contentPane)
            .lineBorder()
            .title(element.label)
            .buildAll()

        // ControlsFX Borders 返回的对象同样是 Node，可直接作为 EventTarget 使用
        (borderedNode as EventTarget).apply {
            setContext(element, env)
        }

        // 将边框节点添加到父容器
        (parent as? Node)?.addChildIfPossible(borderedNode)

        // 统一应用颜色、尺寸等公共属性
        if (borderedNode is Node) {
            setupCommon(borderedNode, element)
        }

        return borderedNode
    }

    private fun visibleChildren(element: BaseContainer): List<BaseElement> {
        return element.children.filterNot {
            it is MathDefinition && !it.visible
        }
    }

    private fun renderHBoxContainer(element: HorizontalContainer, parent: EventTarget, env: RenderEnv): Node {
        return parent.hbox(DEFAULT_GAP) {
            setContext(element, env)

            alignment = Pos.CENTER_LEFT
            // 控制垂直高度，降低最小高度
            minHeight = Region.USE_COMPUTED_SIZE
            prefHeight = Region.USE_COMPUTED_SIZE
            maxHeight = Region.USE_PREF_SIZE
            
            val visibleChildren: List<BaseElement> = visibleChildren(element)
            element.children.forEach { child ->
                if (visibleChildren.size == 2 && child == visibleChildren[1]) {
//                    spacer {}
                }
                val node = renderElement(child, this, env)
                //拉伸子项, 填充了spacer不拉伸
                if (node is Node && !(element.children.size == 2 && child == visibleChildren[1])) {
                    node.hgrow = Priority.ALWAYS
                }
            }
        }
    }

    private fun renderVBoxContainer(element: VerticalContainer, parent: EventTarget, env: RenderEnv): Node {
        return parent.vbox(DEFAULT_GAP) {
            setContext(element, env)

            element.children.forEachIndexed { index, child ->
                val node = renderElement(child, this, env)
                //拉伸子项
                if (node is Node) {
                    node.vgrow = Priority.ALWAYS
                }
            }
        }
    }

    private fun renderGridContainer(element: GridContainer, parent: EventTarget, env: RenderEnv): Node {
        return parent.gridpane {
            setContext(element, env)
            var row = 0
            var col = 0

            element.children.forEach { child ->
                val node = renderElement(child, this, env)
                if (node is Node) {
                    node.gridpaneConstraints {
                        columnRowIndex(col, row)
                        margin = insets(DEFAULT_GAP)
                        vAlignment = VPos.CENTER
                        hGrow = Priority.ALWAYS
                        vGrow = Priority.ALWAYS
                    }
                    col++
                    if (col >= element.columns) {
                        col = 0
                        row++
                    }
                }
            }
        }
    }

    /* ---------------- Control Renderers ---------------- */

    private fun renderButton(element: UIButton, parent: EventTarget): Node {
        return parent.button(element.label) {
            styleClass += Styles.SMALL
            style {
                prefWidth = (-1).px
            }
        }
    }

    private fun renderCheckbox(element: UICheckbox, parent: EventTarget): Node {
        return parent.checkbox(element.label) {
            isSelected = element.default == 1
        }
    }

    private fun renderSwitch(element: UISwitch, parent: EventTarget): Node {
        return parent.switch(element.label) {
            isSelected = element.default == 1
        }
    }

    private fun renderToggleButton(element: UIToggle, parent: EventTarget): Node {
        return parent.togglebutton(element.label) {
            isSelected = element.default == 1
        }
    }

    private fun renderCombo(element: UICombo, parent: EventTarget): Node {
        return parent.combobox(values = element.items) {
            converter = object : StringConverter<ComboItem>() {
                override fun toString(t: ComboItem): String {
                    return t.label
                }

                override fun fromString(string: String?): ComboItem {
                    throw NotImplementedError()
                }
            }
            if (element.default == -1) {
                selectionModel.select(0)
            } else {
                val defaultItem = element.findItem(element.default) ?: throw NoSuchElementException("ComboItem with default value $value not found.")
                selectionModel.select(defaultItem)
            }
        }
    }

    private fun renderRadio(element: UIRadio, parent: EventTarget): Node {
        val group = ToggleGroup()
        return parent.vbox {
            //mark this vbox as ToggleGroup
            properties[FAKE_RADIO_KEY] = group
            element.radioButtons.forEachIndexed { index, radioButton ->
                radiobutton(radioButton.label, group, radioButton.value) {
                    if (index == element.default) {
                        isSelected = true
                    }
                }
            }
        }
    }

    private fun renderText(element: UIText, parent: EventTarget): Node {
        return parent.label(element.label)
    }

    private fun renderLed(element: Led, parent: EventTarget): Node {
        return parent.led(element.offcolor.fxColor(), element.oncolor.fxColor())
    }

    private fun renderSpinner(element: UISpinner, parent: EventTarget): Node {
        return parent.spinner(element.min, element.max, element.default, element.step, true, enableScroll = true) {
            if (element.suffix != null) {
                valueProperty().addListener { _, _, newValue ->
                    editor.text = "$newValue${element.suffix}"
                }
            }
        }
    }

    private fun renderDoubleSpinner(element: UIDoubleSpinner, parent: EventTarget): Node {
        return parent.spinner(element.min, element.max, element.default, element.step, true, enableScroll = true) {
            if (element.suffix != null) {
                valueProperty().addListener { _, _, newValue ->
                    editor.text = "$newValue${element.suffix}"
                }
            }
        }
    }

    private fun renderVLine(parent: EventTarget): Node {
        return parent.separator(Orientation.VERTICAL) {
        }
    }

    private fun renderHLine(parent: EventTarget): Node {
        return parent.separator(Orientation.HORIZONTAL) {
        }
    }

    private fun renderProgressBar(element: UIProgressBar, parent: EventTarget): Node {
        return parent.myprogressbar(element.vertical) {
            progress = element.default
            styleClass += Styles.LARGE
        }
    }

    private fun renderTextInput(parent: EventTarget): Node {
        return parent.textfield {
        }
    }

    private fun renderMath(element: MathDefinition, parent: EventTarget, env: RenderEnv): Node? {
        if (!element.visible) {
            //不可见单独处理, 不会附加到Node.userData
            val context = MathContext(element)
            registerToEnv(context, env)
            return null
        }
        return parent.label()
    }

    private fun renderTimer(element: Timer, env: RenderEnv) {
        val context = TimerContext(element)
        registerToEnv(context, env)
    }

    private fun renderVariable(element: Variable, env: RenderEnv) {
        val context = VariableContext(element)
        registerToEnv(context, env)
    }

    /* ---------------- Helper Methods ---------------- */

    /**
     * for single children container
     */
    fun renderContainerContent(element: BaseContainer, parent: EventTarget, context: RenderEnv) {
        element.children.filter {
            it is Timer || it is Variable || (it is MathDefinition && !it.visible)
        }.forEach {
            renderElement(it, parent, context)
        }
        val containers = element.children.filter { it -> it is BaseContainer }
        if (containers.size > 1) {
            throw IllegalStateException("Only one container is allowed in a ${element::class.simpleName}")
        }
        if (containers.isNotEmpty()) {
            renderElement(containers[0], parent, context)
        }
    }

}