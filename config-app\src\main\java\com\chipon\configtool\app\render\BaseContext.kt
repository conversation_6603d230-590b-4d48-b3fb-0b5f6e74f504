package com.chipon.configtool.app.render

import com.chipon.configtool.core.model.BaseElement
import com.chipon.configtool.core.model.element.DataType
import javafx.beans.binding.NumberExpression

/**
 * Holds runtime value for a element and exposes a JavaFX property for binding.
 *
 * <AUTHOR> Wu
 */
abstract class BaseContext(val holder: BaseElement) {

    val id: String? = holder.id

    abstract val valueProperty: NumberExpression
    val value: Number get() = valueProperty.value

    open fun tooltip(): String {
        return buildString {
            append(holder.getTagName()+" ")
            if (id != null) append("id=$id, ")
            append("value=$value, ")
            append("type=${dataType() }")
        }
    }

    override fun toString(): String {
        return "${this::class.simpleName} id=$id, value=$value"
    }

    open fun isResolved(): Boolean {
        return true
    }

    open fun resolve(contexts: Map<String, BaseContext>) {
        throw UnsupportedOperationException()
    }

    open fun dataType(): DataType {
        throw UnsupportedOperationException()
    }

}

/**
 * Derived context rely on another context to compute it's value.
 */
abstract class DerivedContext(holder: BaseElement) : BaseContext(holder) {

    protected var resolving = false
    protected var resolved = false

    override fun isResolved(): Boolean = resolved

    override fun resolve(contexts: Map<String, BaseContext>) {
        if (resolved) return
        if (resolving) throw IllegalStateException("Circular reference ➜ $id")
        resolving = true

        doResolve(contexts)

        resolved = true
        resolving = false
    }

    abstract fun doResolve(contexts: Map<String, BaseContext>)

}
