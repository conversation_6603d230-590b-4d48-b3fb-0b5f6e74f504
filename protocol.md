# 通用配置工具通信协议详细说明

## 1. 完整数据包格式

所有与设备通信的数据包都遵循统一的格式.一帧数据由固定的开头、cmd、payload、校验和以及结束字节组成。对于可变长度数据的命令（如 `0x41`, `0x51`）,Payload开头会包含长度信息.

```
+-------+-------+-------+-----------+----------+-------+
|  SOF  |  SEQ  |  CMD  |  Payload  | CheckSum |  EOF  |
+-------+-------+-------+-----------+----------+-------+
| 1Byte | 1Byte | 1Byte |  N Bytes  |   1Byte  | 1Byte |
+-------+-------+-------+-----------+----------+-------+
   ^        ^       ^        ^          ^         ^
   |        |       |        |          |         +-- End of Frame 固定为 0x55
   |        |       |        |          +-- 校验和,使用 XOR(^=) 逐字节计算Payload中的数据）
   |        |       |        +-- 命令自定义报文.这部分的内容和结构由对应CMD决定
   |        |       +-- CMD 命令码
   |        +-- 序列号,下位机应该携带与上位机请求一致的SEQ
   +-- Start of Frame 固定为 0xAA
```

---

### 心跳检测

上位机通过某个固定间隔发送心跳包给下位机判断在线状态,下位机响应在线状态,参考数据包[ping](#上位机ping-cmd-0x31), [pong](#下位机pong-cmd-0x32)

## 2. 命令列表 (CMD)

### 上位机ping (CMD: `0x31`)

上位机发送心跳检测

- **`Payload`格式**:
  ```
  +--------+
  |  DATA  |
  +--------+
  | 1 Byte |
  +--------+
     ^
     +-- 任意数据
  ```

### 下位机pong (CMD: `0x32`)

下位机应答心跳

- **`Payload`格式**:
  ```
  +--------+
  |  DATA  |
  +--------+
  | 1 Byte |
  +--------+
     ^
     +-- 和ping数据对应
  ```

### 写变量 (CMD: `0x41`)

向设备的N个指定地址写入 1~8 字节数据.

- **`Payload`格式**:
  ```
  +-------+-------+-------+-----------+-------+-------+-------+-------+
  |  LEN  | ADDR1 | SIZE1 |   DATA1   | ADDR2 | SIZE2 | DATA2 |  ...  |
  +-------+-------+-------+-----------+-------+-------+-------+-------+
  | 2Byte | 2Byte | 1Byte | 1~8 Bytes |  ...  |  ...  |  ...  |  ...  |
  +-------+-------+-------+-----------+-------+-------+-------+-------+
  |  ^        ^        ^        ^
  |  |        |        |        +-- 数据内容,大小由 SIZE 指定 (1~8 字节)
  |  |        |        +-- SIZE 数据长度 (1~8)
  |  |        +-- 目标变量地址
  |  +-- LEN 2字节: 后续所有字节总数
  ```

- **下位机返回报文类型**
  - 参考: [CMD:0x61](#下位机响应命令-cmd-0x61)

- **示例**:
  - 向地址 0x10 写入[DE AD BE],向地址 0x20 写入[EF]。
  - `LEN` = (2+1+3) + (2+1+1) = 00 0A.
  - 完整数据包: AA 00 41 `00 0A` `00 10` 03 `DE AD BE` `00 20` 01 `EF` 10 55
  - 其中checksum=0x10

### 读变量 (CMD: `0x51`)

向设备的N个指定地址读取 1~8 字节数据.

- **`Payload`格式**:
  ```
  +-------+-------+-------+-------+-----+
  |  LEN  | ADDR1 | ADDR2 | ADDRN | ... |
  +-------+-------+-------+-------+-----+
  | 2Byte | 2Byte | 2Byte | 2Byte | ... |
  +-------+-------+-------+-------+-----+
  |  ^        ^
  |  |        |
  |  |        |
  |  |        +-- 目标需要读取的长度 (1~8)
  |  +-- LEN 2字节: 后续所有字节总数
  ```

- **下位机返回报文类型**
  - 成功参考: [CMD:0x41](#写变量-cmd-0x41)
  - 失败参考: [CMD:0x61](#下位机响应命令-cmd-0x61)

- **示例**:
  - 读取地址 0x10 的 3 字节、地址 0x20 的 1 字节。
  - `LEN` = (2+1) + (2+1) = 6.

- **响应**: 与写变量命令 [CMD:0x41](#写变量-cmd-0x41) 的响应规则一致,此处不再重复.

### 下位机响应命令 (CMD: 0x61)

下位机回复结果状态。

- **`Payload`格式**:
  ```
  +--------+
  | STATUS |
  +--------+
  | 1 Byte |
  +--------+
     ^
     +-- 结果：`0x00` 表示成功,其它值为错误码
  ```
#### 当前内置错误码
- 0x01: 芯片故障
- 0x02: 上位机数据包解析错误
- 0x03: 数据访问地址错误

---

## 3. 补充说明

- **字节序**: 报文采用**高字节在前 (Big Endian)** 的顺序.