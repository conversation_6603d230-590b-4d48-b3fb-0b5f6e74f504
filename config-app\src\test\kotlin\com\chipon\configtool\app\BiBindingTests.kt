package com.chipon.configtool.app

import com.chipon.configtool.app.render.UIRenderer
import com.chipon.configtool.app.render.toRadioGroup
import com.chipon.configtool.core.model.control.ComboItem
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.beans.property.IntegerProperty
import javafx.scene.control.ComboBox
import javafx.scene.control.Spinner
import javafx.scene.control.ToggleButton
import javafx.scene.layout.VBox
import org.junit.jupiter.api.Test

class BiBindingTests : TestCase() {

    @Test
    fun `context change updates toggle`() {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <togglebutton id="TG" label="tg" default="0"/>
                </vbox>
            </uicfg>
        """.trimIndent()
        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixtureUI { root ->
            val env = UIRenderer.buildView(profile, root)
            val toggle = (root.content as VBox).children[0] as ToggleButton
            val ctxProp = env.defineContexts["TG"]!!.valueProperty as IntegerProperty

            // ensure initial sync matches UI (0)
            toggle.isSelected shouldBe false
            ctxProp.get() shouldBe 0

            // change context value -> UI should update
            ctxProp.set(1)
            toggle.isSelected shouldBe true
        }
    }

    @Test
    fun `context change updates combobox`() {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <combo id="CB">
                        <item label="A" value="0"/>
                        <item label="B" value="1"/>
                    </combo>
                </vbox>
            </uicfg>
        """.trimIndent()
        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixtureUI { root ->
            val env = UIRenderer.buildView(profile, root)
            val combo = (root.content as VBox).children[0] as ComboBox<*>
            val ctxProp = env.defineContexts["CB"]!!.valueProperty as IntegerProperty

            (combo.selectionModel.selectedItem as ComboItem).value shouldBe 0
            ctxProp.set(1)
            (combo.selectionModel.selectedItem as ComboItem).value shouldBe 1
        }
    }

    @Test
    fun `context change updates spinner`() {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <spinner id="SP" range="0;10" default="0"/>
                </vbox>
            </uicfg>
        """.trimIndent()
        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixtureUI { root ->
            val env = UIRenderer.buildView(profile, root)
            val spinner = (root.content as VBox).children[0] as Spinner<*>
            val ctxProp = env.defineContexts["SP"]!!.valueProperty as IntegerProperty

            spinner.value shouldBe 0
            ctxProp.set(7)
            spinner.value shouldBe 7
        }
    }

    @Test
    fun `context change updates radio`() {
        val xml = """
            <uicfg version="1.0.0">
                <vbox>
                    <radio id="RD">
                        <radiobutton label="r0" value="0"/>
                        <radiobutton label="r1" value="1"/>
                    </radio>
                </vbox>
            </uicfg>
        """.trimIndent()
        val profile = XmlConfigParser.parseFromString(xml)

        setUpFixtureUI { root ->
            val env = UIRenderer.buildView(profile, root)
            val radioGroupBox = (root.content as VBox).children[0] as VBox
            val tg = radioGroupBox.toRadioGroup()
            val ctxProp = env.defineContexts["RD"]!!.valueProperty as IntegerProperty

            (tg.selectedToggle?.groupValue() as Int) shouldBe 0
            ctxProp.set(1)
            (tg.selectedToggle?.groupValue() as Int) shouldBe 1
        }
    }
}
