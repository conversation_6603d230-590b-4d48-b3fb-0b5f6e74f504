package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.*
import com.chipon.configtool.core.model.control.UICombo
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class ComboTest {

    @Test
    fun `test combo basic properties and items`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <hbox>
                                <text label="Voltage Threshold:"/>
                                <combo default="2">
                                    <item label="VRT1" value="0"/>
                                    <item label="VRT2" value="1"/>
                                    <item label="VRT3" value="2"/>
                                </combo>
                            </hbox>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xmlContent)
        val tab = root.children[0] as TabContainer
        val group = tab.children[0] as GroupContainer
        val vbox = group.children[0] as VerticalContainer
        val hbox = vbox.children[0] as HorizontalContainer
        val combo = hbox.children[1] as UICombo

        combo.default shouldBe 2
        combo.items.size shouldBe 3
        combo.items[0].label shouldBe "VRT1"
        combo.items[0].value shouldBe 0
    }

    @Test
    fun `combo default index out of range should report error`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <combo default="2">
                                <item label="A" value="0"/>
                                <item label="B" value="1"/>
                            </combo>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root: ProfileRoot = XmlConfigParser.parseFromString(xmlContent, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("Bad default index") } shouldBe true
    }

    @Test
    fun `combo with no items should report error`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <combo default="0"/>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root: ProfileRoot = XmlConfigParser.parseFromString(xmlContent, false)
        val errors = root.validate()
        println("Errors: $errors")
        errors.shouldNotBeEmpty()
        errors.any { it.contains("no <item> elements") } shouldBe true
    }

    @Test
    fun `combo item lockon parsing`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab label="Main">
                    <group label="Controls">
                        <vbox>
                            <hbox>
                                <text label="dummy"/>
                                <combo default="0">
                                    <item label="OptionA" value="0" lockon="CTRL.GPIO_CTRL[2:0]=4"/>
                                    <item label="OptionB" value="1"/>
                                </combo>
                            </hbox>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xmlContent)
        val tab = root.children[0] as TabContainer
        val group = tab.children[0] as GroupContainer
        val vbox = group.children[0] as VerticalContainer
        val hbox = vbox.children[0] as HorizontalContainer
        val combo = hbox.children[1] as UICombo

        val item0 = combo.items[0]
        item0.label shouldBe "OptionA"
        item0.lockon!!.lockers.size shouldBe 1
        item0.lockon!!.lockers[0].id shouldBe "CTRL.GPIO_CTRL[2:0]"
        item0.lockon!!.lockers[0].state shouldBe 4
    }

    @Test
    fun `combo items should auto-assign sequential indices`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <tab>
                    <group>
                        <vbox>
                            <hbox>
                                <text label="dummy"/>
                                <combo default="1">
                                    <item label="ItemA"/>
                                    <item label="ItemB"/>
                                    <item label="ItemC"/>
                                </combo>
                            </hbox>
                        </vbox>
                    </group>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xmlContent, false)

        val combo = ((((root.children[0] as TabContainer).children[0] as GroupContainer)
            .children[0] as VerticalContainer).children[0] as HorizontalContainer).children[1] as UICombo

        combo.items[0].value shouldBe 0
        combo.items[1].value shouldBe 1
        combo.items[2].value shouldBe 2
    }
} 