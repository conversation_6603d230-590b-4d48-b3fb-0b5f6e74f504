package com.chipon.configtool.net

import com.chipon.configtool.net.handler.PacketCodec
import com.chipon.configtool.net.packet.*
import io.kotest.assertions.throwables.shouldThrowAny
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.netty.buffer.ByteBuf
import io.netty.channel.embedded.EmbeddedChannel
import org.junit.jupiter.api.Test

class PacketCodecTest {

    @Test
    fun `codec ping packet`() {
        val channel = EmbeddedChannel(PacketCodec())

        // Build a simple ping packet
        val original = Packet(0x01, Ping(0x05))
        channel.writeOutbound(original)
        val encoded: ByteBuf = channel.readOutbound()

        // Now feed the encoded bytes back through the inbound path
        channel.writeInbound(encoded.retain()) // retain because channel will release
        val decoded: Packet = channel.readInbound()

        // Verify
        val ping = decoded.payload.shouldBeInstanceOf<Ping>()
        ping.data shouldBe 0x05.toByte()
        decoded.seq shouldBe original.seq

        // cleanup
        encoded.release()
        channel.finishAndReleaseAll()
    }

    @Test
    fun `codec write payload`() {
        val channel = EmbeddedChannel(PacketCodec())
        val vars = listOf(
            VarList(0x10, 3, 0xDEADBE),
            VarList(0x20, 1, 0xEF)
        )
        val original = Packet(0x01, WritePayload(vars))
        channel.writeOutbound(original)
        val encoded: ByteBuf = channel.readOutbound()
        channel.writeInbound(encoded.retain())
        val decoded: Packet = channel.readInbound()
        val payload = decoded.payload.shouldBeInstanceOf<WritePayload>()
        payload.data.size shouldBe vars.size
        payload.data[0].address shouldBe vars[0].address
        encoded.release()
        channel.finishAndReleaseAll()
    }

    @Test
    fun `codec read payload`() {
        val channel = EmbeddedChannel(PacketCodec())
        val addresses = listOf(0x10, 0x20)
        val original = Packet(0x01, ReadPayload(addresses))
        channel.writeOutbound(original)
        val encoded: ByteBuf = channel.readOutbound()
        channel.writeInbound(encoded.retain())
        val decoded: Packet = channel.readInbound()
        val read = decoded.payload.shouldBeInstanceOf<ReadPayload>()
        read.address shouldBe addresses
        encoded.release()
        channel.finishAndReleaseAll()
    }

    @Test
    fun `codec status payload`() {
        val channel = EmbeddedChannel(PacketCodec())
        val original = Packet(0x01, StatusPayload(0x00))
        channel.writeOutbound(original)
        val encoded: ByteBuf = channel.readOutbound()
        channel.writeInbound(encoded.retain())
        val decoded: Packet = channel.readInbound()
        val status = decoded.payload.shouldBeInstanceOf<StatusPayload>()
        status.code shouldBe 0x00.toByte()
        encoded.release()
        channel.finishAndReleaseAll()
    }

    @Test
    fun `parse bad byte array`() {
        val data: ByteArray = byteArrayOf(0x41, 0xFF.toByte())
        shouldThrowAny {
            parsePayload(data)
        }
    }

    @Test
    fun `codec handles concatenated frames`() {
        val enc = EmbeddedChannel( PacketCodec())

        // create two payloads and encode separately
        val p1 = Packet(0x01, Ping(0x11))
        val p2 = Packet(0x01, StatusPayload(0x00))
        enc.writeOutbound(p1)
        val buf1: ByteBuf = enc.readOutbound()
        enc.writeOutbound(p2)
        val buf2: ByteBuf = enc.readOutbound()

        // concatenate buffers
        val merged = io.netty.buffer.Unpooled.wrappedBuffer(buf1.retain(), buf2.retain())

        // new channel for decode test
        val dec = EmbeddedChannel(PacketCodec())
        dec.writeInbound(merged.retain())

        val pkt1: Packet = dec.readInbound()
        pkt1.payload.shouldBeInstanceOf<Ping>()

        val pkt2: Packet = dec.readInbound()
        pkt2.payload.shouldBeInstanceOf<StatusPayload>()

        buf1.release(); buf2.release(); merged.release()
        enc.finishAndReleaseAll(); dec.finishAndReleaseAll()
    }

    @Test
    fun `codec skips malformed frame and decodes next`() {
        val encoder = EmbeddedChannel(PacketCodec())

        // valid ping frame
        val original = Packet(0x01, Ping(0x22))
        encoder.writeOutbound(original)
        val validBuf: ByteBuf = encoder.readOutbound()

        // Corrupt a copy: flip checksum byte (second last)
        val corrupt = validBuf.copy()
        val idxChecksum = corrupt.readableBytes() - 2
        corrupt.setByte(idxChecksum, corrupt.getByte(idxChecksum).toInt() xor 0xFF)

        // merge corrupt + valid
        val merged = io.netty.buffer.Unpooled.wrappedBuffer(corrupt, validBuf.retain())

        val decoder = EmbeddedChannel(PacketCodec())
        decoder.writeInbound(merged.retain())

        // first frame invalid -> codec should drop, second decoded
        val pkt: Packet = decoder.readInbound()
        pkt.payload.shouldBeInstanceOf<Ping>()

        validBuf.release(); merged.release(); encoder.finishAndReleaseAll(); decoder.finishAndReleaseAll()
    }
}