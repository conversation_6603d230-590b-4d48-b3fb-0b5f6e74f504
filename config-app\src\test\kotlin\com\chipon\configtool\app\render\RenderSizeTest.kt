package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.doubles.shouldBeExactly
import javafx.scene.control.Button
import javafx.scene.layout.VBox
import javafx.stage.Stage
import org.junit.jupiter.api.Test
import org.testfx.api.FxToolkit

/**
 * <AUTHOR> Wu
 */
class RenderSizeTest : TestCase() {

    @Test
    fun `should set prefWidth and prefHeight from xml size attributes`() {
        val profile = XmlConfigParser.parseFromString("""
            <uicfg version="1.0.0">
                <vbox>
                    <button id="P1" label="123" size="123;123"/>
                </vbox>
            </uicfg>
        """.trimIndent())

        setUpFixture { root ->
            UIRenderer.buildView(profile, root)

            val content = (root.content as VBox)
            val button = content.children[0] as Button

            button.minWidth shouldBeExactly 123.0
            button.minHeight shouldBeExactly 123.0
        }
    }
}