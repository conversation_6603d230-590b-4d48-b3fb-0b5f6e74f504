<?xml version="1.0" encoding="UTF-8"?>
<ifxmlcfg version="2.1.24">
	<checksum>1f51b0288b136bfd5444a50a727135d8e7c75309f271e3c1b8bfa61ea83aaf16</checksum>
	<!--file TLE94x1Configure.xml                                                            -->
	<!--brief TLE94x1 Lite SBC Configuration                                            -->
	<!--                                                                                -->
	<!--You can use this file under the terms of the IFX License.                       -->
	<!--                                                                                -->
	<!--This file is distributed in the hope that it will be useful, but WITHOUT ANY    -->
	<!--WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR   -->
	<!--A PARTICULAR PURPOSE. See the IFX License for more details (IFX_License.txt).   -->
	<!--                                                                                -->
	<!--This file may be used, copied, and distributed, with or without modification,   -->
	<!--provided that all copyright notices are retained; that all modifications to     -->
	<!--this file are prominently noted in the modified file; and that this paragraph   -->
	<!--is not modified.                                                                -->
	<!--                                                                                -->
	<!--copyright Copyright (C) 2018 Infineon Technologies AG                           -->
	<!--                                                                                -->
	<!--********************************************************************************-->
	<!--                        Author(s) Identity                                      -->
	<!--********************************************************************************-->
	<!-- Initials     Name                                                              -->
	<!-- YR           Yannek Rixen                                                      -->
	<!--********************************************************************************-->
	<!--                                                                                -->
	<!--********************************************************************************-->
	<!--                       Revision Control History                                 -->
	<!--********************************************************************************-->
	<!-- V0.0.1 2018-12-18, YR: Initial version                                         -->
	<!-- V0.0.2 2018-12-18, YR: Calc. CDR upper and lower limits                        -->
	<!-- V0.0.3 2018-12-18, YR: export extended ID bit                                  -->
	<!-- V0.0.4 2018-12-19, YR: rearanged layout                                        -->
	<!-- V0.0.5 2019-01-08, YR: PWM Duty Cycle export as hex value                      -->
	<!-- V0.0.6 2019-01-13, YR: export library                                          -->
	<!--********************************************************************************-->
	<version label="V0.0.6"/>
	<verticalcontainer color="255;255;255;255" sizepolicy="fixed;fixed">
		<var define="CTRL.M_S_CTRL[7:6]"/>	<!-- Required for setting the Mode via buttons -->
		<var define="CTRL.M_S_CTRL"/>		<!-- Required for read back of M_S_CTRL register via SPI and displaying mode -->
		<var define="CTRL.SUP_STAT_1"/>		<!-- Required for display status via LEDs -->
		<var define="CTRL.SUP_STAT_0"/>
		<var define="CTRL.THERM_STAT"/>
		<var define="CTRL.DEV_STAT"/>
		<var define="CTRL.BUS_STAT"/>
		<var define="CTRL.WK_STAT_0"/>
		<var define="CTRL.WK_STAT_1"/>
		<var define="CTRL.WK_LVL_STAT"/>
		<var define="CTRL.GPIO_OC_STAT"/>
		<var define="CTRL.GPIO_OL_STAT"/>
		<var define="CTRL.FAM_PROD_STAT"/><!-- Required for display of product type and mode -->
		<header file="SBC_TLE94x1.h">
			<gridcontainer columns="2">
				<verticalcontainer>
					<groupcontainer label="Control Function Lite-SBC">
						<horizontalcontainer>
						<verticalcontainer>
							<horizontalcontainer>
								<verticalcontainer>
									<groupcontainer label="VCC1">
										<verticalcontainer color="240;240;240">
											<checkbox label="OV Reset active" define="CTRL.M_S_CTRL[2]"/>
											<horizontalcontainer>
												<text label="UV Thresh."/>
												<combo define="CTRL.M_S_CTRL[1:0]">
													<item label="VRT1"/>
													<item label="VRT2"/>
													<item label="VRT3"/>
													<item label="VRT4"/>
												</combo>
											</horizontalcontainer>
											<checkbox label="UV Release on VRT1" define="CTRL.HW_CTRL_1[7]"/>
											<horizontalcontainer>
												<text label="Current Lim."/>
												<combo define="CTRL.HW_CTRL_3[1:0]" default="label=1.0 A">
													<item label="0.75 A"/>
													<item label="1.0 A"/>
													<item label="1.2 A"/>
													<item label="1.5 A"/>
												</combo>
											</horizontalcontainer>
											<checkbox label="High act. Peak Thresh." define="CTRL.HW_CTRL_2[4]"/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="VCC2">
										<verticalcontainer color="240;240;240">
											<combo define="CTRL.M_S_CTRL[4:3]">
												<item label="VCC2 off"/>
												<item label="VCC2 on in Normal Mode"/>
												<item label="VCC2 on in Nor. + Stop M."/>
												<item label="VCC2 always on"/>
											</combo>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="RSTN Pin Behavior">
										<verticalcontainer color="240;240;240">
											<checkbox label="Reduced delay time" define="CTRL.HW_CTRL_1[4]"/>
											<checkbox label="Triggered by Soft Reset" define="CTRL.HW_CTRL_0[6]"/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
							</horizontalcontainer>
							<horizontalcontainer>
								<verticalcontainer>
									<groupcontainer label="GPIO, PWM and other pins">
										<verticalcontainer color="240;240;240">
											<horizontalcontainer>
												<text label="GPIO"/>
												<combo label="GPIO Selection" define="CTRL.GPIO_CTRL[2:0]" default="label=Failure Output">
													<item label="Off" value="4" lockon="CTRL.WK_PUPD_CTRL[7:6]=0"/>
													<item label="Failure Output" value="0" lockon="CTRL.WK_PUPD_CTRL[7:6]=0"/>
													<item label="High-Side Timer" value="3" lockon="CTRL.WK_PUPD_CTRL[7:6]=0"/>
													<item label="Wake Input" value="5"/>
													<item label="Low-Side PWM" value="6" lockon="CTRL.WK_PUPD_CTRL[7:6]=0"/>
													<item label="High-Side PWM" value="7" lockon="CTRL.WK_PUPD_CTRL[7:6]=0"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="Pull Device"/>
												<combo label="Pull Device" define="CTRL.WK_PUPD_CTRL[7:6]">
													<item label="None"/>
													<item label="Pull-down"/>
													<item label="Pull-up"/>
													<item label="Automatic"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="PWM Frequency"/>
												<combo label="PWM Frequency" define="CTRL.PWM_FREQ_CTRL[1:0]" >
													<item label="100 Hz"/>
													<item label="120 Hz"/>
													<item label="325 Hz"/>
													<item label="400 Hz"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="PWM Duty C."/>
												<doublespinbox range="0;100" suffix=" %" define="MATH.PWM_DC" default="0.0" step="5"/>
												<math visible="false" define="CTRL.PWM_CTRL[7:0]" format="%X" formula="MATH.PWM_DC*2.555"/>
											</horizontalcontainer>
											<checkbox label="INTN trig. by all status bits" define="CTRL.WK_CTRL_1[7]"/>
											<checkbox label="CFG1" define="CTRL.HW_CTRL_0[0]"/>
											<checkbox label="FO_EN" define="CTRL.HW_CTRL_0[5]"/>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="CAN Configuration">
										<verticalcontainer color="240;240;240">
											<combo define="CTRL.BUS_CTRL_0[2:0]">
												<item label="OFF"/>
												<item label="Wake capable"/>
												<item label="Receive only"/>
												<item label="Normal"/>
												<item label="Off (SWK)"/>
												<item label="Wake cap. (SWK)"/>
												<item label="Rec. only (SWK)"/>
												<item label="Normal (SWK)"/>
											</combo>
											<checkbox label="Slew Rate Control off" define="CTRL.BUS_CTRL_3[4]"/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="Wake-up">
										<verticalcontainer color="240;240;240">
											<checkbox label="Enable Wake-up" define="CTRL.WK_CTRL_1[0]" default="1"/>
											<horizontalcontainer>
												<checkbox label="Voltage Sensing" define="CTRL.WK_CTRL_1[5]" lockon="CTRL.WK_CTRL_1[0]=0;CTRL.GPIO_CTRL[2:0]=4;CTRL.WK_PUPD_CTRL[1:0]=0;CTRL.HW_CTRL_0[5]=0"/>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="Pull Device"/>
												<combo define="CTRL.WK_PUPD_CTRL[1:0]">
													<item label="None"/>
													<item label="Pull-down"/>
													<item label="Pull-up"/>
													<item label="Automatic"/>
												</combo>
											</horizontalcontainer>
											<checkbox label="Enable WK Timer" define="CTRL.WK_CTRL_0[6]"/>
											<horizontalcontainer>
												<text label="WK Timer Period"/>
												<combo label="WK Timer Period" define="CTRL.TIMER_CTRL[3:0]">
													<item label="10 ms"/>
													<item label="20 ms"/>
													<item label="50 ms"/>
													<item label="100 ms"/>
													<item label="200 ms"/>
													<item label="500 ms"/>
													<item label="1 s"/>
													<item label="2 s"/>
													<item label="5 s"/>
													<item label="10 s"/>
													<item label="20 s"/>
													<item label="50 s"/>
													<item label="100 s"/>
													<item label="200 s"/>
													<item label="500 s"/>
													<item label="1000 s"/>
												</combo>
											</horizontalcontainer>
											<horizontalcontainer>
												<text label="On-time"/>
												<combo label="ON Time" define="CTRL.TIMER_CTRL[6:4]">
													<item label="off, HSx is low"/>
													<item label="0.1 ms"/>
													<item label="0.3 ms"/>
													<item label="1.0 ms"/>
													<item label="10 ms"/>
													<item label="20 ms"/>
													<item label="off, HSx is high"/>
												</combo>
											</horizontalcontainer>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="Thermal Sensing">
										<verticalcontainer color="240;240;240">
											<checkbox label="Incr. waiting time for TSD2" define="CTRL.HW_CTRL_1[5]"/>
											<checkbox label="Incr. threshold for TSD1/2" define="CTRL.HW_CTRL_3[2]"/>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
								<verticalcontainer>
									<groupcontainer label="Watchdog">
										<verticalcontainer color="240;240;240">
										
											<checkbox label="Disable during Stop Mode" define="CTRL.WK_CTRL_0[2];CTRL.WD_CTRL[6]" />
											
											<checkbox label="Starts after CAN Wake" define="CTRL.WD_CTRL[4]"/>
											<radio define="CTRL.WD_CTRL[5]">
												<radiobutton label="Time-out Watchdog"/>
												<radiobutton label="Windows Watchdog"/>
											</radio>
											
											<horizontalcontainer>
												<text label="WD Timer Period"/>
												<combo label="Watchdog Timer Period" define="CTRL.WD_CTRL[2:0]">
													<item label="10 ms"/>
													<item label="20 ms"/>
													<item label="50 ms"/>
													<item label="100 ms"/>
													<item label="200 ms"/>
													<item label="500 ms"/>
													<item label="1000 ms"/>
													<item label="10000 ms"/>
												</combo>
											</horizontalcontainer>
										
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="Charge Pump">
										<verticalcontainer color="240;240;240">
											
											<checkbox label="Charge Pump" define="CTRL.HW_CTRL_0[2]"/>
										
											<text label="Switching Frequency:"/>
											<combo define="CTRL.HW_CTRL_2[7:5]" default="label=2.2 MHz">
												<item label="1.8 MHz"/>
												<item label="2.0 MHz"/>
												<item label="2.2 MHz"/>
												<item label="2.4 MHz"/>
											</combo>
											<text label="Spread Spec. Mod. Freq:"/>
											<combo define="CTRL.HW_CTRL_2[3:2]">
												<item label="off"/>
												<item label="15.625 kHz"/>
												<item label="31.250 kHz"/>
												<item label="62.500 kHz"/>
											</combo>
										</verticalcontainer>
									</groupcontainer>
								</verticalcontainer>
							</horizontalcontainer>
						</verticalcontainer>
							<verticalcontainer>
								<groupcontainer label="CAN PN Control">
									<verticalcontainer color="240;240;240">
										<checkbox label="enable CAN PN" lockinverted="1" define="MATH.EN_PN"
											lockon="CTRL.SWK_CAN_FD_CTRL[0]=0;CTRL.SWK_CAN_FD_CTRL[3:1]=0;MATH.Baudrate=2;MATH.DoubleCDRFreq=0;CTRL.SWK_ID0_CTRL[0]=0;CTRL.SWK_DLC_CTRL[3:0]=0" />
										<checkbox label="CAN FD Tolerant" default="1" define="CTRL.SWK_CAN_FD_CTRL[0]" lockinverted="1" lockon="CTRL.SWK_CAN_FD_CTRL[3:1]=0"/>
										<math visible="false" define="CTRL.SWK_CAN_FD_CTRL[5]" formula="CTRL.SWK_CAN_FD_CTRL[0]"/>
										<horizontalcontainer>
											<text label="Transmission Baudrate    "/> 
											<combo  default="2" define="CTRL.SWK_CAN_FD_CTRL[3:1]">
												<item label="" value="0" />
												<item label="2 Mb" value="2"/>
												<item label="5 Mb" value="4"/>
											</combo>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="Baudrate"/>
											<combo svd="0" define="MATH.Baudrate" default="2">
												<item label="125 kbps" value="0"/>
												<item label="250 kbps" value="1"/>
												<item label="500 kbps" value="2"/>
											</combo>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="CDR Freq.:"/>
											<math visible="false" svd="0" define="CTRL.SWK_CDR_CTRL2[1:0]" formula="(MATH.Baudrate == 0 ? 3 : (MATH.Baudrate == 1 ? 2 : 1)) - MATH.DoubleCDRFreq" />
											<math visible="true" svd="0" define="MATH.CDR_FrequencyMHz" formula="CTRL.SWK_CDR_CTRL2[1:0] == 3 ? 10 : (CTRL.SWK_CDR_CTRL2[1:0] == 2 ? 20 : (CTRL.SWK_CDR_CTRL2[1:0] == 1 ? 40 : 80))" />
											<text label="MHz"/>
											<checkbox label="double CDR Frequenzy" define="MATH.DoubleCDRFreq"/>
											<math visible="false" define="CTRL.SWK_BTL0_CTRL" formula="MATH.DoubleCDRFreq == 1 ? 0xA0 : 0x50" />
											<math visible="false" define="CTRL.SWK_CDR_LIMIT_HIGH_CTRL" formula="MATH.DoubleCDRFreq == 1 ? 0xA8 : 0x54" />
											<math visible="false" define="CTRL.SWK_CDR_LIMIT_LOW_CTRL" formula="MATH.DoubleCDRFreq == 1 ? 0x98 : 0x4C" />
										</horizontalcontainer>
										<checkbox label="extended ID" define="CTRL.SWK_ID0_CTRL[0]"/>
										<text label="Max. values for ID and ID Mask are 0x7FF,"/>
										<text label="or 0x1FFFFFFF when extended."/>
										<math visible="false" svd="0" define="LED.Math_extendedID" formula="(CTRL.SWK_ID0_CTRL[0] == 1) ? ((CTRL.SWK_IDx_CTRL > 0x1FFFFFFF) ? 0 : 1 ) : ((CTRL.SWK_IDx_CTRL > 0x7FF) ? 0 : 1 )"/>
										<math visible="false" svd="0" define="LED.Math_extendedIDMsk" formula="(CTRL.SWK_ID0_CTRL[0] == 1) ? ((CTRL.SWK_MASK_IDx_CTRL > 0x1FFFFFFF) ? 0 : 1 ) : ((CTRL.SWK_MASK_IDx_CTRL > 0x7FF) ? 0 : 1 )"/>
										<horizontalcontainer>
											<text label="Message ID"/>
											<edit label="MessageID" default="0x00000000" define="CTRL.SWK_IDx_CTRL" numeric="1" validator="^0x[0-9A-Fa-f]{8}$">
											</edit>
											<led data="LED.Math_extendedID" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="ID Mask"/>
											<edit label="MessageIDMsk" default="0x00000000" define="CTRL.SWK_MASK_IDx_CTRL" numeric="1" validator="^0x[0-9A-Fa-f]{8}$">
											</edit>
											<led data="LED.Math_extendedIDMsk" bitmask="0x01" oncolor="green" offcolor="red" size="25;25"/>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="Payload length in bytes"/>
											<combo define="CTRL.SWK_DLC_CTRL[3:0]">
												<item label="0"/>
												<item label="1"/>
												<item label="2"/>
												<item label="3"/>
												<item label="4"/>
												<item label="5"/>
												<item label="6"/>
												<item label="7"/>
												<item label="8"/>
											</combo>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="Payload upper bits"/>
											<edit label="MessageH" svd="0" default="0x00000000" define="CTRL.SWK_DATA_H_CTRL" validator="^0x[0-9A-Fa-f]{8}$">
											</edit>
										</horizontalcontainer>
										<horizontalcontainer>
											<text label="Payload lower bits"/>
											<edit label="MessageL" svd="0" default="0x00000000" define="CTRL.SWK_DATA_L_CTRL" validator="^0x[0-9A-Fa-f]{8}$">
											</edit>
										</horizontalcontainer>
									</verticalcontainer>
								</groupcontainer>
								<groupcontainer label="Configuration Lock">
										<verticalcontainer color="240;240;240">
											<checkbox label="Lock GPIO and CP_EN" define="CTRL.HW_CTRL_1[3]"/>
											<checkbox label="Lock rwl bits (excluding GPIO and CP_EN)" define="CTRL.HW_CTRL_2[0]"/>
										</verticalcontainer>
									</groupcontainer>
									<groupcontainer label="Data">
										<horizontalcontainer color="240;240;240">
											<text/>
											<button label="Export" define="EXPORT" svd="0">
												<action event="clicked" cmd="runScript" data="Export()" />
												<action event="clicked" cmd="startProgram" data="cmd /C LightSBCChooseDialog.exe TLE94x1"/>
											</button>
											<text/>
										</horizontalcontainer>
									</groupcontainer>
							</verticalcontainer>
						</horizontalcontainer>
					</groupcontainer>
				</verticalcontainer>
			</gridcontainer>
		</header>
	</verticalcontainer>
</ifxmlcfg>
