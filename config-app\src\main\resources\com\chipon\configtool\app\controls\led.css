/*
 * Copyright (c) 2021 by <PERSON><PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.led {
  -color: red;
}

.led .frame {
  -fx-background-color: linear-gradient(from 14% 14% to 84% 84%,
  rgba(20, 20, 20, 0.64706) 0%,
  rgba(20, 20, 20, 0.64706) 15%,
  rgba(41, 41, 41, 0.64706) 26%,
  rgba(200, 200, 200, 0.40631) 85%,
  rgba(200, 200, 200, 0.3451) 100%);
  -fx-background-radius: 1024px;
}

.led .main {
  -fx-background-color: linear-gradient(from 15% 15% to 83% 83%,
  derive(-color, -0%) 0%,
  derive(-color, -25%) 49%,
  -color 100%);
  -fx-background-radius: 1024px;
}

.led .highlight {
  -fx-background-color: radial-gradient(center 15% 15%, radius 50%, white 0%, transparent 100%);
  -fx-background-radius: 1024;
}