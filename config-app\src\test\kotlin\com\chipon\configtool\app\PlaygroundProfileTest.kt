package com.chipon.configtool.app

import com.chipon.configtool.app.render.UIRenderer
import com.chipon.configtool.core.parser.XmlConfigParser
import org.junit.jupiter.api.Test
import org.testfx.api.FxRobot
import java.io.File

/**
 * Test for the comprehensive playground profile that demonstrates all platform features
 * 
 * <AUTHOR>
 */
class PlaygroundProfileTest : TestCase() {

    companion object {
        val PLAYGROUND_PROFILE: File = File(PROJECT_ROOT, "profiles/PLAYGROUND.xml").absoluteFile
    }

    @Test
    fun `playground profile should load and render successfully`(robot: FxRobot) {
        val profile = XmlConfigParser.parseFromFile(PLAYGROUND_PROFILE)

        lateinit var env: UIRenderer.RenderEnv
        setUpFixtureUI { root ->
            env = UIRenderer.buildView(profile, root)
        }

    }
} 