package com.chipon.configtool.app

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.LoggerContext
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.AppenderBase
import org.fxmisc.richtext.StyleClassedTextArea
import tornadofx.*
import java.util.*


/**
 * Console view log appender
 */
open class ConsoleViewAppender(context: LoggerContext, val logView: StyleClassedTextArea) : AppenderBase<ILoggingEvent>() {

    init {
        name = this::class.simpleName
        setContext(context)

        val rootLogger: Logger = context.getLogger(Logger.ROOT_LOGGER_NAME)
        rootLogger.addAppender(this)
    }

    /**
     * 将日志记录同时保存到文件和窗口
     */
    @Synchronized
    override fun append(event: ILoggingEvent) {
        when (event.level) {
            Level.INFO, Level.WARN, Level.ERROR -> {
                val text = "${DATE_FORMAT.format(Date())} ${event.level} - ${event.message}\n"
                val style = when (event.level) {
                    Level.WARN -> "log-warn"
                    Level.ERROR -> "log-error"
                    else -> "log-default"
                }
                runLater {
                    logView.append(text, style)
                    // Auto-scroll to bottom whenever new log is appended
                    logView.moveTo(logView.length)
                    logView.requestFollowCaret()
                }
            }
        }
    }

}
