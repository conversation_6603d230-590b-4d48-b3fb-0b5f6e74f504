package com.chipon.configtool.core.parser

import com.chipon.configtool.core.model.element.Action
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

/**
 * Unit tests for [Action.validate].
 */
class ActionValidationTest {

    @Test
    fun `root validation catches action missing event`() {
        val xml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <uicfg version="1.0.0">
                <tab label="Main">
                    <button label="Do" size="10;10">
                        <action data="0x00"/>
                    </button>
                </tab>
            </uicfg>
        """.trimIndent()

        val root = XmlConfigParser.parseFromString(xml, false)
        val errors = root.validate()
        errors.shouldNotBeEmpty()
        errors.any { it.contains("event is required") }.shouldBe(true)
        errors.any { it.contains("cmd is required") }.shouldBe(true)
    }

} 