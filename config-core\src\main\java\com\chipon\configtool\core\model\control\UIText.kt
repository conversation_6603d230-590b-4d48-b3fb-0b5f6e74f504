package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IColorable

/**
 * <text>
 */
class UIText : BaseUIControl(), IColorable {
    /**
     * 可能是个占位text
     */
    var label: String = ""
    override var color: String? = null
}

/**
 * <textinput>
 */
class UITextInput : BaseUIControl() {
    lateinit var regex: Regex
}
