# UI配置文件 (uicfg.xml) 开发指南

## 简介

本文档旨在指导您如何创建和配置 `uicfg.xml` 文件. 该文件用于定义软件的用户界面.

## 1. 根标签: `<uicfg>`

所有 UI 配置都必须包含在一个根标签 `<uicfg>` 中.

**属性:**

*   `version`: (可选) 配置文件的版本号, 例如 `version="1.0"`.

**子标签:**

*   `<svd>`: (可选) 用于关联 SVD (System View Description) 文件.
    *   **属性**: `file` - SVD 文件的路径, 例如 `file="path/to/your.svd"`.
*   各类**容器标签** (见下文).

**使用规则:**

*   如果 `<uicfg>` 下有多个子标签, 那么这些子标签必须全部是 `<tab>`. 如果只有一个子标签, 则可以是任意类型的容器.

---

## 2. 通用概念与属性

许多界面元素都具有一些通用的属性和功能.

### 2.1. 通用属性

*   `id`: (可选) 一个在整个配置文件中唯一的标识符. 主要用于**锁定功能**.
*   `size`: (可选) 设置元素的尺寸.
    *   格式为 `"宽度;高度"`, 例如 `size="100;30"`.
    *   如果只提供一个数字, 例如 `size="100"`, 则表示宽度和高度相同.
    *   `-1` 表示自动计算尺寸.
*   `readonly`: (可选) 设置控件是否为只读.
    *   `0`: 可读写 (默认).
    *   `1`: 只读.
*   `label`: 大多数可见控件都支持此属性, 用于显示标题或说明文字. 对于某些控件 (如 `<group>`), 此属性是必需的.
*   `color`: (可选) 设置元素的颜色 (例如文字颜色或边框颜色).
    *   格式为十六进制 (HEX), 例如 `color="#FF0000"` (红色) 或 `color="#F00"`.

### 2.2. 锁定功能 (`lockon`)

一些控件 (如复选框, 下拉框的选项) 可以根据自身的状态, 去禁用或启用界面上的其他控件. 这通过 `lockon` 属性实现.

*   **目标**: `lockon` 的值指向一个或多个其他控件的 `id`.
*   **格式**: `lockon="id1,id2:state,..."`
    *   `id1`: 要控制的目标控件的 `id`.
    *   `id2:state`: 当**当前控件**的值变为 `state` 时, 锁定 `id2` 控件. 如果不指定 `state`, 默认为 `0`.

**示例**: 一个复选框 `<checkbox id="enable_feature" ... />` 和一个输入框 `<spinner id="feature_value" ... />`.
如果在输入框上设置 `lockon="enable_feature:1"`, 那么只有当 ID 为 `enable_feature` 的复选框被选中 (值为1) 时, 这个输入框才可用.

---

## 3. 容器标签

容器用于组织和布局其他控件, 它们可以相互嵌套.

*   **`<tab>` (选项卡)**
    *   功能: 创建一个选项卡界面, 每个 `<tab>` 是一个可切换的页面.
    *   必需属性: `label` - 选项卡的标题.
*   **`<group>` (分组框)**
    *   功能: 用一个带标题的边框将一组控件包围起来.
    *   必需属性: `label` - 分组框的标题.
*   **`<grid>` (网格布局)**
    *   功能: 将其内部的控件排列成一个网格.
    *   属性: `columns` - 网格的列数, 默认为 `2`.
*   **`<hbox>` (水平布局)**
    *   功能: 将其内部的控件水平排列.
*   **`<vbox>` (垂直布局)**
    *   功能: 将其内部的控件垂直排列.

---

## 4. 控件标签

控件是用户可以直接交互的界面元素.

*   **`<button>` (按钮)**
    *   功能: 一个可以点击的按钮.
    *   必需属性: `label` - 按钮上显示的文字.
    *   子标签: `<action>` - 定义点击按钮时执行的动作.
*   **`<checkbox>` (复选框)**, **`<switch>` (开关)**, **`<togglebutton>` (切换按钮)**
    *   功能: 提供一个开关选项, UI 样式不同但功能相同.
    *   必需属性: `label` - 显示的文字.
    *   属性:
        *   `default`: 默认状态. `0` 为未选中, `1` 为选中. 默认为 `0`.
        *   `lockon`: 可用于锁定其他控件.
    *   子标签: `<action>` - 定义状态改变时执行的动作.
*   **`<combo>` (下拉框)**
    *   功能: 提供一个下拉选择列表.
    *   属性: `default` - 默认选中的**选项值 (value)**. 如果不设置, 则不选中任何项.
    *   子标签: `<item>`
        *   **`<item>` (下拉选项)**
            *   必需属性: `label`, `value`.
            *   `label`: 选项显示的文字.
            *   `value`: 选项的实际值 (一个整数).
            *   `lockon`: 当此项被选中时, 可以锁定其他控件.
    *   **验证规则**:
        *   必须包含至少一个 `<item>`.
        *   `default` 属性设置的值必须是某个 `<item>` 的 `value`.
*   **`<radio>` (单选按钮组)**
    *   功能: 提供一组选项, 用户只能选择其中一个.
    *   属性: `default` - 默认选中的**选项索引** (从 `0` 开始). 默认为 `0`.
    *   子标签: `<radiobutton>`
        *   **`<radiobutton>` (单选按钮)**
            *   必需属性: `label`, `value`.
            *   `label`: 按钮旁显示的文字.
            *   `value`: 按钮的实际值 (一个整数).
    *   **验证规则**:
        *   必须包含至少一个 `<radiobutton>`.
        *   `default` 的值必须在有效索引范围内 (0 到 按钮总数-1).
*   **`<spinner>` (整数调节器)**
    *   功能: 用于输入或调整整数值.
    *   属性:
        *   `range`: (可选) 允许的数值范围. 格式: `"最小值-最大值"`, 例如 `"0-100"`.
        *   `step`: (可选) 每次调整的步长, 默认为 `1`.
        *   `default`: (可选) 默认值, 默认为 `0`.
    *   **验证规则**:
        *   `range` 的最小值不能大于最大值.
        *   `default` 值必须在 `range` 范围内.
        *   `step` 必须是正数.
*   **`<doublespinner>` (小数调节器)**
    *   功能: 用于输入或调整小数值 (浮点数).
    *   属性:
        *   `range`: (可选) 允许的数值范围. 格式: `"最小值-最大值"`, 例如 `"0.0-10.0"`.
        *   `step`: (可选) 每次调整的步长.
        *   `default`: (可选) 默认值, 默认为 `0.0`.
        *   `suffix`: (可选) 在数值后显示的单位或后缀, 例如 `suffix="V"`.
*   **`<led>` (状态灯)**
    *   功能: 根据某个数据的特定位 (bit) 来显示不同颜色的灯, 用于状态指示.
    *   必需属性:
        *   `data`: 关联的数据变量名.
        *   `bitmask`: 位掩码, 用于从 `data` 中提取状态位. 可以是十进制数 (如 `255`) 或十六进制数 (如 `0xFF`).
    *   属性:
        *   `oncolor`: 状态为 "on" (位为1) 时的颜色. 可选值: `green`, `red`, `grey`. 默认为 `green`.
        *   `offcolor`: 状态为 "off" (位为0) 时的颜色. 可选值同上, 默认为 `grey`.
*   **`<text>` (静态文本)**
    *   功能: 显示一段固定的文字.
    *   属性: `label` - 要显示的文字内容.
*   **`<hline>` / `<vline>` (水平/垂直分割线)**
    *   功能: 用于在视觉上分割控件.

---

## 5. 功能标签

这些标签提供更高级的功能, 通常作为其他控件的子标签或独立存在.

*   **`<math>` (数学计算)**
    *   功能: 根据一个公式动态计算并显示结果.
    *   必需属性: `formula` - 计算公式. 公式中可以直接使用其他控件的 `id` 作为变量.
    *   属性:
        *   `visible`: `true` 或 `false`, 控制是否显示计算结果. 默认为 `true`.
        *   `type`: (可选) 定义数据类型, 如 `uint8`, `uint16`, `double` 等.
        *   `format`: (可选) C 语言风格的格式化字符串, 用于控制输出格式, 例如 `%.2f` 表示保留两位小数.
        *   `unit`: (可选) 在结果后显示的单位.
*   **`<timer>` (定时器)**
    *   功能: 周期性地触发一个或多个 `<action>`.
    *   属性:
        *   `interval`: (可选) 触发间隔, 单位是毫秒 (ms).
        *   `oneshot`: (可选) `true` 或 `false`. `true` 表示只触发一次.
        *   `run`: (可选) `true` 或 `false`. `true` 表示立即开始计时.
    *   子标签: `<action>` - 定时器触发时执行的动作.
*   **`<action>` (动作)**
    *   功能: 定义一个具体操作, 可被按钮, 定时器等触发.
    *   属性:
        *   `event`: (可选) 触发事件类型.
        *   `cmd`: (可选) 命令类型. 可选值: `sendUSB` (发送USB数据), `setState` (设置状态), `resetGUI` (重置界面).
        *   `data`: (可选) 发送的数据.
        *   `recdata`: (可选) 接收的数据.
