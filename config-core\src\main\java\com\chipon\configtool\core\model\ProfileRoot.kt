package com.chipon.configtool.core.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

/**
 * Root element for UI XML configuration files.
 * Represents the <uicfg> element with version attribute and child elements.
 */
@JacksonXmlRootElement(localName = "uicfg")
class ProfileRoot {

    var version: String? = null

    var svd: SvdFile? = null

    var children: MutableList<BaseContainer> = mutableListOf()

    fun validate(): List<String> {
        val errors = mutableListOf<String>()

        fun traverse(element: BaseElement, path: String) {
            if (element is IValidatable) {
                errors += element.validate().map { "$path : $it" }
            }
            if (element is IDisplayable) {
                errors += element.validateLabel().map { "$path : $it" }
            }
            if (element is IColorable) {
                errors += element.validateColor().map { "$path : $it" }
            }
            if (element is IAction) {
                element.actions.mapIndexed { index, action ->
                    errors += action.validate().map { "$path/action[$index] : $it" }
                }
            }
            if (element is BaseContainer) {
                element.children.forEachIndexed { index, child ->
                    traverse(child, "$path/${child.getTagName()}[$index]")
                }
            }
        }

        if (children.size > 1 && !children.all { it is TabContainer }) {
            errors += "uicfg的子项只有tab才可以放置多个"
        }

        children.forEachIndexed { index, child ->
            traverse(child, "${child.getTagName()}[$index]")
        }

        return errors
    }
}

class SvdFile {

    @JacksonXmlProperty(isAttribute = true)
    var file: String? = null
}
