package com.chipon.configtool.core.model.control

import com.chipon.configtool.core.model.BaseUIControl
import com.chipon.configtool.core.model.IAction
import com.chipon.configtool.core.model.IColorable
import com.chipon.configtool.core.model.IDisplayable
import com.chipon.configtool.core.model.element.Action
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

/**
 * <button>
 */
data class UIButton(

    override var label: String,

    override var color: String? = null,

    @set:JacksonXmlProperty(localName = "action")
    override var actions: List<Action> = emptyList()
) : BaseUIControl(), IAction, IDisplayable, IColorable
