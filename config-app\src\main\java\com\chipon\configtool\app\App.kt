package com.chipon.configtool.app

import com.chipon.configtool.net.IClient
import javafx.application.Application.launch
import javafx.scene.image.Image
import javafx.stage.FileChooser
import tornadofx.*
import java.io.File
import java.lang.management.ManagementFactory
import java.text.SimpleDateFormat

/**
 * <AUTHOR>
 */
class App : tornadofx.App(
    icon = Image(App::class.java.classLoader.getResourceAsStream("icon.png")),
) {

    override val primaryView = HexonWorkspace::class

    init {
        importStylesheet(App::class.java.getResource("my.css")!!.toExternalForm())
        importStylesheet(App::class.java.getResource("my-override.css")!!.toExternalForm())
    }
}

const val APP_NAME = "Hexon"
const val APP_VERSION = "1.0.0"
val XML_FILTER = FileChooser.ExtensionFilter("XML files", "*.xml")
val DEBUG_MODE: Boolean = ManagementFactory.getRuntimeMXBean().inputArguments.any { it.contains("-agentlib:jdwp") }

val DATE_FORMAT = SimpleDateFormat("yyyy-MM-dd.HH:mm:ss")
val PROJECT_ROOT: File = run {
    var dir = File("").absoluteFile
    while (dir.parentFile != null) {
        if (File(dir, "profiles").exists()) {
            return@run dir
        }
        dir = dir.parentFile
    }
    throw IllegalStateException("Project root directory not found (no profiles found in parent directories)")
}

val hexFormat = HexFormat { number.prefix = "0x" }
var client: IClient? = null

fun main(args: Array<String>) {
    launch(App::class.java, *args)
}