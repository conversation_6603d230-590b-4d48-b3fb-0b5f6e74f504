package com.chipon.configtool.app.render

import com.chipon.configtool.app.TestCase
import com.chipon.configtool.app.controls.LedControl
import com.chipon.configtool.app.fxColor
import com.chipon.configtool.app.led
import com.chipon.configtool.core.model.control.LedColor
import com.chipon.configtool.core.parser.XmlConfigParser
import io.kotest.matchers.shouldBe
import javafx.scene.control.CheckBox
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import tornadofx.*

/**
 * <AUTHOR> Wu
 */
class RenderLedTest : TestCase() {

    @Test
    fun `led should toggle when checkbox is toggled`() {
        val xmlContent = """
            <uicfg version="1.0.0">
                <vbox>
                    <checkbox id="CHK_ENABLE" label="checkbox"/>
                    <led data="CHK_ENABLE" bitmask="0x01" oncolor="green" offcolor="red" size="15;15"/>
                </vbox>
            </uicfg> 
        """.trimIndent()

        val profile = XmlConfigParser.parseFromString(xmlContent)

        setUpFixture { root ->
            // Build the UI from the profile
            UIRenderer.buildView(profile, root)

            // Locate the controls in the rendered scene graph
            val checkbox = root.content.lookup(".check-box") as CheckBox
            val led = root.content.lookup(".led") as LedControl

            // Initially unchecked -> LED off
            checkbox.isSelected shouldBe false
            led.stateProperty().get() shouldBe false

            // Toggle ON -> LED on
            checkbox.isSelected = true
            led.stateProperty().get() shouldBe true

            // Toggle OFF again -> LED off
            checkbox.isSelected = false
            led.stateProperty().get() shouldBe false
        }
    }

    @Disabled("仅用于手动观察 UI，不要在 CI 里跑")
    @Test
    fun `test led`() {
        setUpFixtureShow { root ->
            root.hbox {
                vbox {
                    led(LedColor.green.fxColor(), LedColor.green.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                    }
                    led(LedColor.red.fxColor(), LedColor.red.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                    }
                    led(LedColor.grey.fxColor(), LedColor.grey.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                    }
                }
                vbox {
                    led(LedColor.green.fxColor(), LedColor.grey.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                        state = true
                    }
                    led(LedColor.red.fxColor(), LedColor.green.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                        state = true
                    }
                    led(LedColor.grey.fxColor(), LedColor.red.fxColor()) {
                        prefHeight = 100.0
                        prefWidth = 100.0
                        state = true
                    }
                }
            }
        }
    }

}