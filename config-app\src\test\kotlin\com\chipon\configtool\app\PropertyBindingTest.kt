package com.chipon.configtool.app

import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import tornadofx.*

class PropertyBindingTest {

    @Test
    fun `test javafx property binding`() {
        // 定义源属性 (模拟 VariableContext)
        val source0 = intProperty(0)
        val source = intProperty(0)
        val target = intProperty(0)

        source.bind(integerBinding(source0) {
            source0.value + 10
        })
        target.bind(integerBinding(source) {
            source.value + 10
        })

        target.value shouldBe 20

        source0.value = 1
        target.value shouldBe 21
    }

    @Test
    fun `test javafx property binding2`() {
        val source0 = intProperty(0)
        val source = intProperty(0)
        val target = intProperty(0)

        source.bind(integerBinding(source0) {
            source0.value + 10
        })
        target.bind(integerBinding(source) {
            source.value + 10
        })

        target.value shouldBe 20

        source0.value = 1
        target.value shouldBe 21
    }
} 