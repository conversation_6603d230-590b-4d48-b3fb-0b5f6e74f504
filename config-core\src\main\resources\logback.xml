<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss:SSS} %highlight(%-5level) %white(-) %-20(%yellow([%20.20thread])) %-55(%cyan(.%method\(%file:%line\))) %highlight(- %msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>log/log.txt</file>
        <append>true</append>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss} %-5level - %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <root level="debug">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>